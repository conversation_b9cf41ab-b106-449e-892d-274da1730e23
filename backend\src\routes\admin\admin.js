const express = require('express')
const { Op } = require('sequelize')
const { sequelize } = require('../../config/database')
const { authenticateAdmin } = require('../../middleware/auth')
const { logAdminAction, createAdminLog } = require('../../middleware/adminLogger')
const {
  User,
  Admin,
  LotteryPeriod,
  PrizeConfig,
  LotteryRecord,
  UserLotteryLimit,
  SystemSetting,
  Banner,
  AdminLog,
  UserLog,
  UserPrizeConfig
} = require('../../models')

const router = express.Router()

/**
 * 获取仪表板统计数据
 * GET /api/admin/dashboard/stats
 */
router.get('/dashboard/stats', authenticateAdmin, async (req, res) => {
  try {
    // 获取基础统计数据
    const [
      totalUsers,
      activeUsers,
      totalPeriods,
      activePeriods,
      totalRecords,
      winRecords,
      totalPayout
    ] = await Promise.all([
      User.count(),
      User.count({ where: { status: '正常' } }),
      LotteryPeriod.count(),
      LotteryPeriod.count({ where: { status: '进行中' } }),
      LotteryRecord.count(),
      LotteryRecord.count({ where: { isWinner: true } }),
      LotteryRecord.sum('prizeAmount', { where: { isWinner: true } })
    ])

    // 获取今日统计
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const [
      todayRecords,
      todayWinRecords,
      todayPayout,
      todayNewUsers
    ] = await Promise.all([
      LotteryRecord.count({
        where: {
          drawnAt: {
            [Op.gte]: today,
            [Op.lt]: tomorrow
          }
        }
      }),
      LotteryRecord.count({
        where: {
          isWinner: true,
          drawnAt: {
            [Op.gte]: today,
            [Op.lt]: tomorrow
          }
        }
      }),
      LotteryRecord.sum('prizeAmount', {
        where: {
          isWinner: true,
          drawnAt: {
            [Op.gte]: today,
            [Op.lt]: tomorrow
          }
        }
      }),
      User.count({
        where: {
          createdAt: {
            [Op.gte]: today,
            [Op.lt]: tomorrow
          }
        }
      })
    ])

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          activeUsers,
          totalPeriods,
          activePeriods,
          totalRecords,
          winRecords,
          totalPayout: parseFloat(totalPayout || 0),
          winRate: totalRecords > 0 ? ((winRecords / totalRecords) * 100).toFixed(2) : 0
        },
        today: {
          records: todayRecords,
          winRecords: todayWinRecords,
          payout: parseFloat(todayPayout || 0),
          newUsers: todayNewUsers,
          winRate: todayRecords > 0 ? ((todayWinRecords / todayRecords) * 100).toFixed(2) : 0
        }
      }
    })
  } catch (error) {
    console.error('获取仪表板统计错误:', error)
    res.status(500).json({
      success: false,
      error: '获取统计数据失败'
    })
  }
})

/**
 * 获取最近活动
 * GET /api/admin/dashboard/activities
 */
router.get('/dashboard/activities', authenticateAdmin, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10

    // 获取最近的抽奖记录
    const recentLotteryRecords = await LotteryRecord.findAll({
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['username', 'platformId']
        },
        {
          model: LotteryPeriod,
          as: 'period',
          attributes: ['periodName']
        }
      ],
      order: [['drawnAt', 'DESC']],
      limit: Math.floor(limit * 0.6), // 60%的活动来自抽奖记录
      raw: false
    })

    // 获取最近注册的用户
    const recentUsers = await User.findAll({
      attributes: ['username', 'platformId', 'createdAt'],
      order: [['createdAt', 'DESC']],
      limit: Math.floor(limit * 0.3), // 30%的活动来自新用户注册
      raw: false
    })

    // 获取最近的期次状态变更
    const recentPeriods = await LotteryPeriod.findAll({
      attributes: ['periodName', 'status', 'updatedAt'],
      order: [['updatedAt', 'DESC']],
      limit: Math.floor(limit * 0.1), // 10%的活动来自期次状态变更
      raw: false
    })

    // 合并并格式化活动数据
    const activities = []

    // 添加抽奖记录活动
    recentLotteryRecords.forEach(record => {
      const timeAgo = getTimeAgo(record.drawnAt)
      if (record.isWinner) {
        activities.push({
          id: `lottery_${record.id}`,
          title: `用户 ${record.user.platformId} 中奖`,
          description: `在"${record.period.periodName}"中获得 ¥${parseFloat(record.prizeAmount).toFixed(2)}`,
          time: timeAgo,
          type: 'lottery_win',
          timestamp: record.drawnAt
        })
      } else {
        activities.push({
          id: `lottery_${record.id}`,
          title: `用户 ${record.user.platformId} 参与抽奖`,
          description: `参与"${record.period.periodName}"抽奖活动`,
          time: timeAgo,
          type: 'lottery_participate',
          timestamp: record.drawnAt
        })
      }
    })

    // 添加新用户注册活动
    recentUsers.forEach(user => {
      const timeAgo = getTimeAgo(user.createdAt)
      activities.push({
        id: `user_${user.id}`,
        title: '新用户注册',
        description: `用户 ${user.platformId} 完成注册`,
        time: timeAgo,
        type: 'user_register',
        timestamp: user.createdAt
      })
    })

    // 添加期次状态变更活动
    recentPeriods.forEach(period => {
      const timeAgo = getTimeAgo(period.updatedAt)
      activities.push({
        id: `period_${period.id}`,
        title: '期次状态更新',
        description: `"${period.periodName}"状态变更为${period.status}`,
        time: timeAgo,
        type: 'period_update',
        timestamp: period.updatedAt
      })
    })

    // 按时间排序并限制数量
    activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
    const limitedActivities = activities.slice(0, limit)

    res.json({
      success: true,
      data: limitedActivities
    })
  } catch (error) {
    console.error('获取最近活动错误:', error)
    res.status(500).json({
      success: false,
      error: '获取最近活动失败'
    })
  }
})

// 辅助函数：计算时间差
function getTimeAgo(date) {
  const now = new Date()
  const diffMs = now - new Date(date)
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  return new Date(date).toLocaleDateString('zh-CN')
}

/**
 * 获取用户列表
 * GET /api/admin/users
 */
router.get('/users', authenticateAdmin, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search = '', 
      status = '' 
    } = req.query
    
    const offset = (parseInt(page) - 1) * parseInt(limit)

    // 构建查询条件
    const whereCondition = {}
    if (search) {
      whereCondition[Op.or] = [
        { username: { [Op.like]: `%${search}%` } },
        { platformId: { [Op.like]: `%${search}%` } }
      ]
    }
    if (status) {
      whereCondition.status = status
    }

    // 查询用户列表
    const { count, rows: users } = await User.findAndCountAll({
      where: whereCondition,
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset
    })

    // 获取每个用户的抽奖统计
    const userIds = users.map(user => user.id)
    const userStats = await LotteryRecord.findAll({
      where: { userId: { [Op.in]: userIds } },
      attributes: [
        'userId',
        [sequelize.fn('COUNT', sequelize.col('id')), 'lotteryCount'],
        [sequelize.fn('SUM', sequelize.literal('CASE WHEN is_winner = 1 THEN 1 ELSE 0 END')), 'winCount'],
        [sequelize.fn('SUM', sequelize.literal('CASE WHEN is_winner = 1 THEN prize_amount ELSE 0 END')), 'totalWinAmount']
      ],
      group: ['userId'],
      raw: true
    })

    // 创建用户统计映射
    const statsMap = {}
    userStats.forEach(stat => {
      statsMap[stat.userId] = {
        lotteryCount: parseInt(stat.lotteryCount) || 0,
        winCount: parseInt(stat.winCount) || 0,
        totalWinAmount: parseFloat(stat.totalWinAmount) || 0
      }
    })

    // 格式化用户数据
    const formattedUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      platformId: user.platformId,
      status: user.status,
      totalWinnings: parseFloat(user.totalWinnings),
      customLotteryEnabled: user.customLotteryEnabled || false,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      ...statsMap[user.id] || { lotteryCount: 0, winCount: 0, totalWinAmount: 0 }
    }))

    res.json({
      success: true,
      data: {
        users: formattedUsers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / parseInt(limit))
        }
      }
    })
  } catch (error) {
    console.error('获取用户列表错误:', error)
    res.status(500).json({
      success: false,
      error: '获取用户列表失败'
    })
  }
})

/**
 * 更新用户状态
 * PUT /api/admin/users/:id
 */
router.put('/users/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const { status, totalWinnings, password } = req.body

    const user = await User.findByPk(id)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      })
    }

    const updateData = {}
    if (status !== undefined) {
      updateData.status = status
    }
    if (totalWinnings !== undefined) {
      updateData.totalWinnings = parseFloat(totalWinnings)
    }

    // 更新密码（如果提供了新密码）
    if (password && password.trim() !== '') {
      if (password.length < 6) {
        return res.status(400).json({
          success: false,
          error: '密码长度至少6位'
        })
      }
      const bcrypt = require('bcryptjs')
      updateData.passwordHash = await bcrypt.hash(password, 10)
    }

    const oldStatus = user.status
    const oldTotalWinnings = user.totalWinnings

    await user.update(updateData)

    // 记录操作日志
    const changes = []
    if (status !== undefined && status !== oldStatus) {
      changes.push(`状态: ${oldStatus} → ${status}`)
    }
    if (totalWinnings !== undefined && totalWinnings !== oldTotalWinnings) {
      changes.push(`总奖金: ${oldTotalWinnings} → ${totalWinnings}`)
    }
    if (password && password.trim() !== '') {
      changes.push('密码已重置')
    }

    if (changes.length > 0) {
      const riskLevel = status === '禁用' || (password && password.trim() !== '') ? '高' : '中'
      await createAdminLog(
        req.admin.id,
        '用户管理',
        `修改用户 ${user.platformId} (${user.username}) 信息: ${changes.join(', ')}`,
        riskLevel,
        {
          targetUserId: user.platformId,
          targetUsername: user.username,
          changes: changes,
          oldValues: { status: oldStatus, totalWinnings: oldTotalWinnings },
          newValues: { status: user.status, totalWinnings: user.totalWinnings },
          timestamp: new Date().toISOString()
        },
        req
      )
    }

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: {
        user: {
          id: user.id,
          username: user.username,
          platformId: user.platformId,
          status: user.status,
          totalWinnings: parseFloat(user.totalWinnings),
          updatedAt: user.updatedAt
        }
      }
    })
  } catch (error) {
    console.error('更新用户信息错误:', error)
    res.status(500).json({
      success: false,
      error: '更新用户信息失败'
    })
  }
})

/**
 * 获取用户抽奖率配置
 * GET /api/admin/users/:id/prize-configs
 */
router.get('/users/:id/prize-configs', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params

    // 检查用户是否存在
    const user = await User.findByPk(id)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      })
    }

    // 获取用户的JSON格式抽奖率配置
    const userPrizeConfig = await UserPrizeConfig.findOne({
      where: { userId: id }
    })

    if (!userPrizeConfig) {
      return res.status(404).json({
        success: false,
        error: '用户抽奖率配置不存在'
      })
    }

    // 解析JSON配置数据
    const configs = userPrizeConfig.prizeConfigs?.configs || []

    res.json({
      success: true,
      data: {
        configs: configs,
        totalProbability: parseFloat(userPrizeConfig.totalProbability),
        isEnabled: userPrizeConfig.isEnabled,
        customLotteryEnabled: user.customLotteryEnabled || false
      }
    })
  } catch (error) {
    console.error('获取用户抽奖率配置错误:', error)
    res.status(500).json({
      success: false,
      error: '获取用户抽奖率配置失败'
    })
  }
})

/**
 * 更新用户抽奖率配置
 * PUT /api/admin/users/:id/prize-configs
 */
router.put('/users/:id/prize-configs', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const { configs, customLotteryEnabled, totalProbability } = req.body

    // 验证总概率是否为100%
    if (Math.abs(totalProbability - 1.0) > 0.0001) {
      return res.status(400).json({
        success: false,
        error: `总概率必须为100%，当前为${(totalProbability * 100).toFixed(2)}%`
      })
    }

    // 检查用户是否存在
    const user = await User.findByPk(id)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      })
    }

    // 开始事务
    const transaction = await sequelize.transaction()

    try {
      // 更新用户的个人抽奖率开关
      await user.update(
        { customLotteryEnabled: customLotteryEnabled },
        { transaction }
      )

      // 更新用户的JSON格式抽奖率配置
      await UserPrizeConfig.update(
        {
          prizeConfigs: { configs: configs },
          totalProbability: totalProbability,
          isEnabled: customLotteryEnabled
        },
        {
          where: { userId: id },
          transaction
        }
      )

      // 记录管理员操作日志
      await createAdminLog(
        req.admin.id,
        '用户抽奖设置',
        `更新用户 ${user.platformId} (${user.username}) 的抽奖率配置`,
        '中',
        {
          targetUserId: user.platformId,
          targetUsername: user.username,
          customLotteryEnabled: customLotteryEnabled,
          totalProbability: totalProbability,
          configsCount: configs.length,
          timestamp: new Date().toISOString()
        },
        req,
        transaction
      )

      await transaction.commit()

      res.json({
        success: true,
        message: '抽奖率配置更新成功'
      })
    } catch (error) {
      await transaction.rollback()
      throw error
    }
  } catch (error) {
    console.error('更新用户抽奖率配置错误:', error)
    res.status(500).json({
      success: false,
      error: '更新用户抽奖率配置失败'
    })
  }
})

/**
 * 一键设置用户不中奖
 * POST /api/admin/users/:id/set-no-win
 */
router.post('/users/:id/set-no-win', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params

    // 检查用户是否存在
    const user = await User.findByPk(id)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      })
    }

    // 调用JSON版本的存储过程
    const result = await sequelize.query(
      'CALL sp_set_user_no_win_json(:userId, :adminId)',
      {
        replacements: {
          userId: id,
          adminId: req.admin.id
        },
        type: sequelize.QueryTypes.SELECT
      }
    )

    res.json({
      success: true,
      message: '已设置为不中奖状态',
      data: result[0]
    })
  } catch (error) {
    console.error('设置用户不中奖错误:', error)
    res.status(500).json({
      success: false,
      error: '设置用户不中奖失败'
    })
  }
})

/**
 * 获取抽奖记录列表
 * GET /api/admin/lottery-records
 */
router.get('/lottery-records', authenticateAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      periodId = '',
      isWinner = '',
      startDate = '',
      endDate = '',
      search = ''
    } = req.query
    
    const offset = (parseInt(page) - 1) * parseInt(limit)

    // 构建查询条件
    const whereCondition = {}
    if (periodId) {
      whereCondition.periodId = parseInt(periodId)
    }
    if (isWinner !== '') {
      whereCondition.isWinner = isWinner === 'true'
    }
    if (startDate && endDate) {
      whereCondition.drawnAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      }
    }

    // 构建用户搜索条件
    const userInclude = {
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'platformId'],
      required: false
    }

    // 如果有搜索关键词，添加用户搜索条件
    if (search) {
      userInclude.where = {
        [Op.or]: [
          { username: { [Op.like]: `%${search}%` } },
          { platformId: { [Op.like]: `%${search}%` } }
        ]
      }
      userInclude.required = true // 内连接，只返回匹配的记录
    }

    // 查询记录列表
    const { count, rows: records } = await LotteryRecord.findAndCountAll({
      where: whereCondition,
      include: [
        userInclude,
        {
          model: LotteryPeriod,
          as: 'period',
          attributes: ['id', 'periodName', 'periodNumber']
        }
      ],
      order: [['drawnAt', 'DESC']],
      limit: parseInt(limit),
      offset
    })

    // 计算统计数据
    const totalRecords = count
    const winRecords = await LotteryRecord.count({
      where: { ...whereCondition, isWinner: true }
    })
    const totalPayout = await LotteryRecord.sum('prizeAmount', {
      where: { ...whereCondition, isWinner: true }
    })

    // 格式化记录数据
    const formattedRecords = records.map(record => ({
      id: record.id,
      platformId: record.user ? record.user.platformId : record.platformId,
      periodId: record.periodId,
      user: record.user ? {
        id: record.user.id,
        username: record.user.username,
        platformId: record.user.platformId
      } : null,
      period: record.period ? {
        id: record.period.id,
        periodName: record.period.periodName,
        periodNumber: record.period.periodNumber
      } : null,
      prizeLevel: record.prizeLevel,
      prizeName: record.prizeName,
      prizeAmount: parseFloat(record.prizeAmount),
      isWinner: record.isWinner,
      status: record.status,
      drawnAt: record.drawnAt,
      drawResult: record.drawResult
    }))

    res.json({
      success: true,
      data: {
        records: formattedRecords,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalRecords,
          totalPages: Math.ceil(totalRecords / parseInt(limit))
        },
        statistics: {
          totalRecords,
          winRecords,
          winRate: totalRecords > 0 ? ((winRecords / totalRecords) * 100).toFixed(2) : 0,
          totalPayout: parseFloat(totalPayout || 0)
        }
      }
    })
  } catch (error) {
    console.error('获取抽奖记录错误:', error)
    res.status(500).json({
      success: false,
      error: '获取抽奖记录失败'
    })
  }
})

/**
 * 发放奖品
 * PUT /api/admin/lottery-records/:id/distribute
 */
router.put('/lottery-records/:id/distribute', authenticateAdmin, async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const recordId = req.params.id
    const { remark = '' } = req.body

    // 查找抽奖记录
    const record = await LotteryRecord.findByPk(recordId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'platformId', 'totalWinnings']
        },
        {
          model: LotteryPeriod,
          as: 'period',
          attributes: ['id', 'periodName']
        }
      ],
      transaction
    })

    if (!record) {
      await transaction.rollback()
      return res.status(404).json({
        success: false,
        error: '抽奖记录不存在'
      })
    }

    // 检查是否为中奖记录
    if (!record.isWinner) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '该记录不是中奖记录，无需发放'
      })
    }

    // 检查是否已经发放
    if (record.status === '已发放') {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '该奖品已经发放过了'
      })
    }

    // 更新记录状态
    await record.update({
      status: '已发放',
      distributedAt: new Date(),
      distributedBy: req.admin.id,
      distributionRemark: remark
    }, { transaction })

    // 更新用户总中奖金额
    if (record.prizeAmount > 0) {
      await record.user.update({
        totalWinnings: parseFloat(record.user.totalWinnings || 0) + parseFloat(record.prizeAmount)
      }, { transaction })
    }

    await transaction.commit()

    // 记录操作日志
    await createAdminLog(
      req.admin.id,
      '奖品发放',
      `发放奖品给用户 ${record.user.platformId}，奖品：${record.prizeName}，金额：¥${record.prizeAmount}`,
      req.ip
    )

    res.json({
      success: true,
      message: '奖品发放成功',
      data: {
        recordId: record.id,
        prizeName: record.prizeName,
        prizeAmount: record.prizeAmount,
        distributedAt: new Date()
      }
    })

  } catch (error) {
    await transaction.rollback()
    console.error('发放奖品错误:', error)
    res.status(500).json({
      success: false,
      error: '发放奖品失败'
    })
  }
})

/**
 * 获取期次列表
 * GET /api/admin/lottery-periods
 */
router.get('/lottery-periods', authenticateAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status = ''
    } = req.query

    const offset = (parseInt(page) - 1) * parseInt(limit)

    // 构建查询条件
    const whereCondition = {}
    if (status) {
      whereCondition.status = status
    }

    // 查询期次列表
    const { count, rows: periods } = await LotteryPeriod.findAndCountAll({
      where: whereCondition,
      include: [{
        model: PrizeConfig,
        as: 'prizes',
        where: { isActive: true },
        required: false
      }],
      order: [['periodNumber', 'DESC']],
      limit: parseInt(limit),
      offset
    })

    // 获取每个期次的统计数据
    const periodIds = periods.map(period => period.id)
    const periodStats = await LotteryRecord.findAll({
      where: { periodId: { [Op.in]: periodIds } },
      attributes: [
        'periodId',
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalRecords'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('user_id'))), 'participantCount'],
        [sequelize.fn('SUM', sequelize.literal('CASE WHEN is_winner = 1 THEN prize_amount ELSE 0 END')), 'totalPayout']
      ],
      group: ['periodId'],
      raw: true
    })

    // 创建期次统计映射
    const statsMap = {}
    periodStats.forEach(stat => {
      statsMap[stat.periodId] = {
        totalRecords: parseInt(stat.totalRecords) || 0,
        participantCount: parseInt(stat.participantCount) || 0,
        totalPayout: parseFloat(stat.totalPayout) || 0
      }
    })

    // 格式化期次数据
    const formattedPeriods = periods.map(period => ({
      id: period.id,
      periodName: period.periodName,
      periodNumber: period.periodNumber,
      startTime: period.startTime,
      endTime: period.endTime,
      drawTime: period.drawTime,
      status: period.status,
      maxAttemptsPerUser: period.maxAttemptsPerUser,
      totalParticipants: period.totalParticipants,
      totalAttempts: period.totalAttempts,
      totalPrizeAmount: parseFloat(period.totalPrizeAmount) || 0,
      actualDrawableAmount: parseFloat(period.actualDrawableAmount) || 0,
      totalShares: period.totalShares || 0,
      createdAt: period.createdAt,
      ...statsMap[period.id] || { totalRecords: 0, participantCount: 0, totalPayout: 0 },
      prizesCount: period.prizes ? period.prizes.length : 0
    }))

    res.json({
      success: true,
      data: {
        periods: formattedPeriods,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / parseInt(limit))
        }
      }
    })
  } catch (error) {
    console.error('获取期次列表错误:', error)
    res.status(500).json({
      success: false,
      error: '获取期次列表失败'
    })
  }
})

/**
 * 创建新期次
 * POST /api/admin/lottery-periods
 */
router.post('/lottery-periods', authenticateAdmin, async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const {
      periodName,
      startTime,
      endTime,
      drawTime,
      maxAttemptsPerUser = 3,
      totalPrizeAmount = 0,
      actualDrawableAmount = 0,
      totalShares = 0,
      prizes = []
    } = req.body

    // 参数验证
    if (!periodName || !startTime || !endTime) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '请填写完整的期次信息'
      })
    }

    // 如果没有提供抽奖时间，默认设置为结束时间
    const finalDrawTime = drawTime || endTime

    // 检查是否存在活跃期次（防止重复创建）
    const currentTime = new Date()
    const activePeriod = await LotteryPeriod.findOne({
      where: {
        status: {
          [Op.in]: ['未开始', '进行中']
        },
        endTime: {
          [Op.gt]: currentTime
        }
      },
      transaction
    })

    if (activePeriod) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: `当前存在活跃期次"${activePeriod.periodName}"（状态：${activePeriod.status}），请等待该期次结束后再创建新期次`
      })
    }

    // 获取下一个期次编号
    const lastPeriod = await LotteryPeriod.findOne({
      order: [['periodNumber', 'DESC']],
      transaction
    })
    const nextPeriodNumber = lastPeriod ? lastPeriod.periodNumber + 1 : 1

    // 创建期次
    const period = await LotteryPeriod.create({
      periodName,
      periodNumber: nextPeriodNumber,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      drawTime: new Date(finalDrawTime),
      status: '未开始',
      maxAttemptsPerUser: parseInt(maxAttemptsPerUser),
      totalParticipants: 0,
      totalAttempts: 0,
      totalPrizeAmount: parseFloat(totalPrizeAmount) || 0,
      actualDrawableAmount: parseFloat(actualDrawableAmount) || 0,
      totalShares: parseInt(totalShares) || 0
    }, { transaction })

    // 创建奖品配置
    if (prizes.length > 0) {
      const prizeConfigs = prizes.map((prize, index) => {
        // 如果前端发送的是百分比，需要转换为小数
        let probabilityValue = parseFloat(prize.probability)
        if (probabilityValue > 1) {
          probabilityValue = probabilityValue / 100 // 转换百分比为小数
        }

        return {
          periodId: period.id,
          prizeLevel: prize.prizeLevel || (index + 1),
          prizeName: prize.prizeName,
          prizeAmount: parseFloat(prize.prizeAmount),
          probability: probabilityValue,
          maxWinners: prize.maxWinners || -1,
          currentWinners: 0,
          colorClass: prize.colorClass || '',
          sortOrder: prize.sortOrder || (index + 1),
          isActive: true
        }
      })

      await PrizeConfig.bulkCreate(prizeConfigs, { transaction })
    }

    await transaction.commit()

    // 记录操作日志
    await createAdminLog(
      req.admin.id,
      '期次管理',
      `创建新抽奖期次：${periodName} (第${nextPeriodNumber}期)`,
      '中',
      {
        periodName: periodName,
        periodNumber: nextPeriodNumber,
        startTime: startTime,
        endTime: endTime,
        drawTime: finalDrawTime,
        maxAttemptsPerUser: maxAttemptsPerUser,
        totalPrizeAmount: totalPrizeAmount,
        actualDrawableAmount: actualDrawableAmount,
        totalShares: totalShares,
        prizesCount: prizes.length,
        timestamp: new Date().toISOString()
      },
      req
    )

    res.json({
      success: true,
      message: '期次创建成功',
      data: {
        period: {
          id: period.id,
          periodName: period.periodName,
          periodNumber: period.periodNumber,
          startTime: period.startTime,
          endTime: period.endTime,
          drawTime: period.drawTime,
          status: period.status,
          maxAttemptsPerUser: period.maxAttemptsPerUser,
          totalPrizeAmount: parseFloat(period.totalPrizeAmount),
          actualDrawableAmount: parseFloat(period.actualDrawableAmount),
          totalShares: period.totalShares,
          createdAt: period.createdAt
        }
      }
    })
  } catch (error) {
    await transaction.rollback()
    console.error('创建期次错误:', error)
    res.status(500).json({
      success: false,
      error: '创建期次失败'
    })
  }
})

/**
 * 更新期次状态
 * PUT /api/admin/lottery-periods/:id/status
 */
router.put('/lottery-periods/:id/status', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const { status } = req.body

    const period = await LotteryPeriod.findByPk(id)
    if (!period) {
      return res.status(404).json({
        success: false,
        error: '期次不存在'
      })
    }

    const oldStatus = period.status
    await period.update({ status })

    // 记录操作日志
    await createAdminLog(
      req.admin.id,
      '期次管理',
      `更新期次 ${period.periodName} (第${period.periodNumber}期) 状态: ${oldStatus} → ${status}`,
      '中',
      {
        periodId: period.id,
        periodName: period.periodName,
        periodNumber: period.periodNumber,
        oldStatus: oldStatus,
        newStatus: status,
        timestamp: new Date().toISOString()
      },
      req
    )

    res.json({
      success: true,
      message: '期次状态更新成功',
      data: {
        period: {
          id: period.id,
          periodName: period.periodName,
          status: period.status,
          updatedAt: period.updatedAt
        }
      }
    })
  } catch (error) {
    console.error('更新期次状态错误:', error)
    res.status(500).json({
      success: false,
      error: '更新期次状态失败'
    })
  }
})

/**
 * 获取系统设置
 * GET /api/admin/settings
 */
router.get('/settings', authenticateAdmin, async (req, res) => {
  try {
    // 获取通用奖品配置（不绑定特定期次）
    const prizeConfig = await PrizeConfig.findAll({
      where: {
        isActive: true
      },
      order: [['sortOrder', 'ASC']]
    })

    // 从数据库获取系统设置
    const systemSettings = {
      platformName: await SystemSetting.getValue('platform_name', '抽奖平台'),
      customerServiceQQ: await SystemSetting.getValue('customer_service_qq', ''),
      customerServiceWechat: await SystemSetting.getValue('customer_service_wechat', ''),
      defaultMaxAttempts: await SystemSetting.getValue('default_max_attempts', 3),
      maintenanceMode: await SystemSetting.getValue('maintenance_mode', false),
      maintenanceNotice: await SystemSetting.getValue('maintenance_notice', '系统维护中，请稍后再试...')
    }

    // 从数据库获取广告横幅
    const bannerList = await Banner.getActiveBanners()
    const banners = bannerList.map(banner => ({
      id: banner.id,
      title: banner.title,
      url: banner.url,
      imageUrl: banner.image_url,
      enabled: banner.enabled,
      sort: banner.sort_order,
      description: banner.description,
      startTime: banner.start_time,
      endTime: banner.end_time
    }))

    res.json({
      success: true,
      data: {
        systemSettings,
        prizeConfig: prizeConfig.map(prize => ({
          id: prize.id,
          level: prize.prizeLevel,
          name: prize.prizeName,
          amount: parseFloat(prize.prizeAmount),
          probability: parseFloat(prize.probability) * 100, // 转换为百分比返回给前端
          isActive: prize.isActive
        })),
        banners
      }
    })
  } catch (error) {
    console.error('获取系统设置错误:', error)
    res.status(500).json({
      success: false,
      error: '获取系统设置失败'
    })
  }
})

/**
 * 更新系统设置
 * PUT /api/admin/settings
 */
router.put('/settings', authenticateAdmin, async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const { systemSettings, prizeConfig, banners } = req.body

    // 更新系统设置
    if (systemSettings && typeof systemSettings === 'object') {
      const settingPromises = []

      if (systemSettings.platformName !== undefined) {
        settingPromises.push(SystemSetting.setValue('platform_name', systemSettings.platformName, 'string'))
      }
      if (systemSettings.customerServiceQQ !== undefined) {
        settingPromises.push(SystemSetting.setValue('customer_service_qq', systemSettings.customerServiceQQ, 'string'))
      }
      if (systemSettings.customerServiceWechat !== undefined) {
        settingPromises.push(SystemSetting.setValue('customer_service_wechat', systemSettings.customerServiceWechat, 'string'))
      }
      if (systemSettings.defaultMaxAttempts !== undefined) {
        settingPromises.push(SystemSetting.setValue('default_max_attempts', systemSettings.defaultMaxAttempts, 'number'))
      }
      if (systemSettings.maintenanceMode !== undefined) {
        settingPromises.push(SystemSetting.setValue('maintenance_mode', systemSettings.maintenanceMode, 'boolean'))
      }
      if (systemSettings.maintenanceNotice !== undefined) {
        settingPromises.push(SystemSetting.setValue('maintenance_notice', systemSettings.maintenanceNotice, 'string'))
      }

      await Promise.all(settingPromises)
    }

    // 更新横幅配置
    if (banners && Array.isArray(banners)) {
      // 删除所有现有横幅
      await Banner.destroy({
        where: {},
        transaction
      })

      // 创建新的横幅配置
      if (banners.length > 0) {
        const newBanners = banners.map((banner, index) => ({
          title: banner.title || '',
          url: banner.url || '',
          image_url: banner.imageUrl || '',
          enabled: banner.enabled !== false,
          sort_order: banner.sort || (index + 1),
          description: banner.description || '',
          start_time: banner.startTime || null,
          end_time: banner.endTime || null,
          click_count: 0,
          is_active: true
        }))

        await Banner.bulkCreate(newBanners, { transaction })
      }
    }

    // 更新奖品配置
    if (prizeConfig && Array.isArray(prizeConfig)) {
      // 遍历前端传来的奖品配置，根据ID更新现有配置
      for (const prize of prizeConfig) {
        if (prize.id) {
          // 将百分比转换为小数（前端发送的是百分比，数据库存储的是小数）
          const probabilityDecimal = parseFloat(prize.probability) / 100

          // 验证概率值范围
          if (probabilityDecimal < 0 || probabilityDecimal > 1) {
            throw new Error(`奖品 "${prize.name}" 的概率值无效: ${prize.probability}%`)
          }

          // 更新现有配置
          await PrizeConfig.update({
            prizeName: prize.name,
            prizeAmount: parseFloat(prize.amount),
            probability: probabilityDecimal,
            isActive: prize.isActive !== false
          }, {
            where: { id: prize.id },
            transaction
          })
        }
      }
    }

    await transaction.commit()

    // 记录操作日志
    const changes = []
    if (systemSettings) changes.push('系统设置')
    if (prizeConfig) changes.push('奖品配置')
    if (banners) changes.push('横幅配置')

    await createAdminLog(
      req.admin.id,
      '系统设置',
      `修改系统设置: ${changes.join(', ')}`,
      '中',
      {
        changes: changes,
        systemSettings: systemSettings || null,
        prizeConfigCount: prizeConfig ? prizeConfig.length : 0,
        bannersCount: banners ? banners.length : 0,
        timestamp: new Date().toISOString()
      },
      req
    )

    res.json({
      success: true,
      message: '系统设置更新成功'
    })
  } catch (error) {
    await transaction.rollback()
    console.error('更新系统设置错误:', error)
    res.status(500).json({
      success: false,
      error: '更新系统设置失败'
    })
  }
})

/**
 * 获取管理员列表
 * GET /api/admin/admins
 */
router.get('/admins', authenticateAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query
    const offset = (parseInt(page) - 1) * parseInt(limit)

    const { count, rows: admins } = await Admin.findAndCountAll({
      attributes: ['id', 'username', 'email', 'role', 'status', 'lastLoginAt', 'lastLoginIp', 'createdAt'],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset
    })

    res.json({
      success: true,
      data: {
        admins,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / parseInt(limit))
        }
      }
    })
  } catch (error) {
    console.error('获取管理员列表错误:', error)
    res.status(500).json({
      success: false,
      error: '获取管理员列表失败'
    })
  }
})

/**
 * 创建管理员
 * POST /api/admin/admins
 */
router.post('/admins', authenticateAdmin, async (req, res) => {
  try {
    const { username, password, email, role = '管理员' } = req.body

    // 参数验证
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        error: '请填写用户名和密码'
      })
    }

    // 检查用户名是否已存在
    const existingAdmin = await Admin.findOne({ where: { username } })
    if (existingAdmin) {
      return res.status(400).json({
        success: false,
        error: '管理员用户名已存在'
      })
    }

    // 加密密码
    const bcrypt = require('bcryptjs')
    const hashedPassword = await bcrypt.hash(password, 10)

    // 创建管理员
    const admin = await Admin.create({
      username,
      passwordHash: hashedPassword,
      email,
      role,
      status: '正常'
    })

    // 记录操作日志
    await createAdminLog(
      req.admin.id,
      '管理员管理',
      `创建新管理员账户: ${username} (${role})`,
      '高',
      {
        newAdminUsername: username,
        newAdminEmail: email,
        newAdminRole: role,
        newAdminId: admin.id,
        timestamp: new Date().toISOString()
      },
      req
    )

    res.json({
      success: true,
      message: '管理员创建成功',
      data: {
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          role: admin.role,
          status: admin.status,
          createdAt: admin.createdAt
        }
      }
    })
  } catch (error) {
    console.error('创建管理员错误:', error)
    res.status(500).json({
      success: false,
      error: '创建管理员失败'
    })
  }
})

/**
 * 更新管理员状态
 * PUT /api/admin/admins/:id
 */
router.put('/admins/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params
    const { username, email, role, status, password } = req.body

    const admin = await Admin.findByPk(id)
    if (!admin) {
      return res.status(404).json({
        success: false,
        error: '管理员不存在'
      })
    }

    // 检查用户名是否已被其他管理员使用
    if (username && username !== admin.username) {
      const existingAdmin = await Admin.findOne({ where: { username } })
      if (existingAdmin) {
        return res.status(400).json({
          success: false,
          error: '用户名已存在'
        })
      }
    }

    const updateData = {}

    // 更新用户名
    if (username !== undefined && username !== admin.username) {
      updateData.username = username
    }

    // 更新邮箱
    if (email !== undefined) {
      updateData.email = email
    }

    // 更新角色
    if (role !== undefined) {
      updateData.role = role
    }

    // 更新状态
    if (status !== undefined) {
      updateData.status = status
    }

    // 更新密码（如果提供了新密码）
    if (password && password.trim() !== '') {
      const bcrypt = require('bcryptjs')
      updateData.passwordHash = await bcrypt.hash(password, 10)
    }

    // 记录变更内容
    const changes = []
    const oldValues = {}
    const newValues = {}

    if (updateData.username) {
      changes.push(`用户名: ${admin.username} → ${updateData.username}`)
      oldValues.username = admin.username
      newValues.username = updateData.username
    }
    if (updateData.email !== undefined) {
      changes.push(`邮箱: ${admin.email || '无'} → ${updateData.email || '无'}`)
      oldValues.email = admin.email
      newValues.email = updateData.email
    }
    if (updateData.role) {
      changes.push(`角色: ${admin.role} → ${updateData.role}`)
      oldValues.role = admin.role
      newValues.role = updateData.role
    }
    if (updateData.status) {
      changes.push(`状态: ${admin.status} → ${updateData.status}`)
      oldValues.status = admin.status
      newValues.status = updateData.status
    }
    if (updateData.passwordHash) {
      changes.push('密码已重置')
    }

    await admin.update(updateData)

    // 记录操作日志
    if (changes.length > 0) {
      const riskLevel = updateData.status === '禁用' || updateData.passwordHash ? '高' : '中'
      await createAdminLog(
        req.admin.id,
        '管理员管理',
        `修改管理员 ${admin.username} 信息: ${changes.join(', ')}`,
        riskLevel,
        {
          targetAdminId: admin.id,
          targetAdminUsername: admin.username,
          changes: changes,
          oldValues: oldValues,
          newValues: newValues,
          timestamp: new Date().toISOString()
        },
        req
      )
    }

    res.json({
      success: true,
      message: '管理员信息更新成功',
      data: {
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          role: admin.role,
          status: admin.status,
          updatedAt: admin.updatedAt
        }
      }
    })
  } catch (error) {
    console.error('更新管理员信息错误:', error)
    console.error('错误详情:', {
      name: error.name,
      message: error.message,
      code: error.code,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    })
    res.status(500).json({
      success: false,
      error: '更新管理员信息失败'
    })
  }
})

/**
 * 删除管理员
 * DELETE /api/admin/admins/:id
 */
router.delete('/admins/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params

    // 检查管理员是否存在
    const admin = await Admin.findByPk(id)
    if (!admin) {
      return res.status(404).json({
        success: false,
        error: '管理员不存在'
      })
    }

    // 不能删除自己
    if (admin.id === req.admin.id) {
      return res.status(400).json({
        success: false,
        error: '不能删除自己的账户'
      })
    }

    // 删除管理员
    await admin.destroy()

    res.json({
      success: true,
      message: '管理员删除成功'
    })
  } catch (error) {
    console.error('删除管理员错误:', error)
    res.status(500).json({
      success: false,
      error: '删除管理员失败'
    })
  }
})

/**
 * 清理过期数据
 * DELETE /api/admin/data/clean
 */
router.delete('/data/clean', authenticateAdmin, async (req, res) => {
  try {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    let deletedRecords = 0

    // 检查表是否存在，如果不存在则跳过
    try {
      // 清理30天前的抽奖记录（保留中奖记录）
      deletedRecords = await LotteryRecord.destroy({
        where: {
          createdAt: {
            [Op.lt]: thirtyDaysAgo
          },
          isWinner: false // 只删除未中奖的记录
        }
      })
    } catch (tableError) {
      console.log('LotteryRecord表不存在或查询失败，跳过清理:', tableError.message)
      // 如果表不存在，不算作错误，继续执行
    }

    // 这里可以添加更多清理逻辑，比如清理日志表等

    const message = deletedRecords > 0
      ? `成功清理 ${deletedRecords} 条过期数据`
      : '没有找到需要清理的过期数据'

    // 记录操作日志
    await createAdminLog(
      req.admin.id,
      '数据管理',
      `清理过期数据: ${message}`,
      '中',
      {
        deletedRecords: deletedRecords,
        cleanupDate: thirtyDaysAgo.toISOString(),
        timestamp: new Date().toISOString()
      },
      req
    )

    res.json({
      success: true,
      message,
      data: {
        deletedRecords,
        cleanDate: thirtyDaysAgo.toISOString()
      }
    })
  } catch (error) {
    console.error('清理过期数据失败:', error)
    res.status(500).json({
      success: false,
      error: '清理过期数据失败: ' + error.message
    })
  }
})

/**
 * 导出用户数据
 * GET /api/admin/data/export/users
 */
router.get('/data/export/users', authenticateAdmin, async (req, res) => {
  try {
    let users = []
    let usersWithStats = []

    try {
      // 获取所有用户数据
      users = await User.findAll({
        attributes: [
          'id', 'platformId', 'username', 'status',
          'totalWinnings', 'createdAt', 'updatedAt'
        ],
        order: [['createdAt', 'DESC']]
      })
    } catch (userError) {
      console.log('User表不存在或查询失败:', userError.message)
      // 如果用户表不存在，返回空数据
      return res.json({
        success: true,
        message: '用户表不存在，返回空数据',
        data: []
      })
    }

    if (users.length === 0) {
      return res.json({
        success: true,
        message: '没有用户数据',
        data: []
      })
    }

    // 获取每个用户的抽奖统计
    usersWithStats = await Promise.all(users.map(async (user) => {
      let stats = { totalAttempts: 0, totalWins: 0, totalWinAmount: 0 }

      try {
        // 统计用户的抽奖次数（总记录数）
        const totalAttempts = await LotteryRecord.count({
          where: { userId: user.id }
        })

        // 统计用户的中奖次数（isWinner = true 或 prizeName != '谢谢参与'）
        const totalWins = await LotteryRecord.count({
          where: {
            userId: user.id,
            isWinner: true
          }
        })

        // 统计用户的总中奖金额（所有 prizeAmount 的总和）
        const totalWinAmount = await LotteryRecord.sum('prizeAmount', {
          where: {
            userId: user.id,
            isWinner: true
          }
        })

        stats = {
          totalAttempts: totalAttempts || 0,
          totalWins: totalWins || 0,
          totalWinAmount: parseFloat(totalWinAmount || 0)
        }
      } catch (recordError) {
        console.log('LotteryRecord表查询失败，使用默认统计:', recordError.message)
      }

      return {
        用户ID: user.id,
        平台ID: user.platformId,
        用户名: user.username,
        状态: user.status,
        总中奖金额: stats.totalWinAmount, // 使用从lottery_records计算的实际金额
        抽奖次数: stats.totalAttempts,
        中奖次数: stats.totalWins,
        注册时间: user.createdAt,
        更新时间: user.updatedAt
      }
    }))

    res.json({
      success: true,
      message: `成功导出 ${usersWithStats.length} 条用户数据`,
      data: usersWithStats
    })
  } catch (error) {
    console.error('导出用户数据失败:', error)
    res.status(500).json({
      success: false,
      error: '导出用户数据失败: ' + error.message
    })
  }
})

/**
 * 导出抽奖记录
 * GET /api/admin/data/export/lottery
 */
router.get('/data/export/lottery', authenticateAdmin, async (req, res) => {
  try {
    let records = []

    try {
      // 获取所有抽奖记录
      records = await LotteryRecord.findAll({
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['platformId', 'username'],
            required: false // 左连接，即使用户不存在也显示记录
          },
          {
            model: LotteryPeriod,
            as: 'period',
            attributes: ['periodName', 'periodNumber'],
            required: false // 左连接，即使期次不存在也显示记录
          }
        ],
        order: [['drawnAt', 'DESC']] // 修复：使用正确的字段名
      })
    } catch (recordError) {
      console.error('LotteryRecord查询失败详细错误:', recordError)
      // 如果抽奖记录表不存在，返回空数据
      return res.json({
        success: true,
        message: '抽奖记录表查询失败: ' + recordError.message,
        data: []
      })
    }

    if (records.length === 0) {
      return res.json({
        success: true,
        message: '没有抽奖记录',
        data: []
      })
    }

    const exportData = records.map(record => ({
      记录ID: record.id,
      用户平台ID: record.user?.platformId || '',
      用户名: record.user?.username || '',
      期次名称: record.period?.periodName || '',
      期次编号: record.period?.periodNumber || '',
      奖品名称: record.prizeName,
      是否中奖: record.isWinner ? '是' : '否',
      中奖金额: record.prizeAmount || 0, // 修复：使用正确的字段名 prizeAmount
      抽奖时间: record.drawnAt, // 修复：使用正确的字段名 drawnAt
      创建时间: record.createdAt
    }))

    res.json({
      success: true,
      message: `成功导出 ${exportData.length} 条抽奖记录`,
      data: exportData
    })
  } catch (error) {
    console.error('导出抽奖记录失败:', error)
    res.status(500).json({
      success: false,
      error: '导出抽奖记录失败: ' + error.message
    })
  }
})

/**
 * 导出今天的抽奖记录
 * GET /api/admin/data/export/lottery/today
 */
router.get('/data/export/lottery/today', authenticateAdmin, async (req, res) => {
  try {
    let records = []

    try {
      // 获取今天的开始和结束时间
      const today = new Date()
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)

      // 获取今天的抽奖记录
      records = await LotteryRecord.findAll({
        where: {
          drawnAt: {
            [Op.between]: [startOfDay, endOfDay]
          }
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['platformId', 'username'],
            required: false // 左连接，即使用户不存在也显示记录
          },
          {
            model: LotteryPeriod,
            as: 'period',
            attributes: ['periodName', 'periodNumber'],
            required: false // 左连接，即使期次不存在也显示记录
          }
        ],
        order: [['drawnAt', 'DESC']]
      })
    } catch (recordError) {
      console.error('LotteryRecord查询失败详细错误:', recordError)
      // 如果抽奖记录表不存在，返回空数据
      return res.json({
        success: true,
        message: '抽奖记录表查询失败: ' + recordError.message,
        data: []
      })
    }

    if (records.length === 0) {
      return res.json({
        success: true,
        message: '今天没有抽奖记录',
        data: []
      })
    }

    const exportData = records.map(record => ({
      记录ID: record.id,
      用户平台ID: record.user?.platformId || '',
      用户名: record.user?.username || '',
      期次名称: record.period?.periodName || '',
      期次编号: record.period?.periodNumber || '',
      奖品名称: record.prizeName,
      是否中奖: record.isWinner ? '是' : '否',
      中奖金额: record.prizeAmount || 0,
      抽奖时间: record.drawnAt,
      创建时间: record.createdAt
    }))

    res.json({
      success: true,
      message: `成功导出今天 ${exportData.length} 条抽奖记录`,
      data: exportData
    })
  } catch (error) {
    console.error('导出今天抽奖记录失败:', error)
    res.status(500).json({
      success: false,
      error: '导出今天抽奖记录失败: ' + error.message
    })
  }
})

/**
 * 重置所有数据
 * DELETE /api/admin/data/reset
 */
router.delete('/data/reset', authenticateAdmin, async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    let resetResults = {
      lotteryRecords: 0,
      userLimits: 0,
      users: 0,
      periodsReset: 0,
      prizesReset: 0
    }

    // 删除所有抽奖记录
    try {
      resetResults.lotteryRecords = await LotteryRecord.destroy({
        where: {},
        transaction
      })
    } catch (error) {
      console.log('LotteryRecord表不存在，跳过删除:', error.message)
    }

    // 删除所有用户抽奖限制
    try {
      resetResults.userLimits = await UserLotteryLimit.destroy({
        where: {},
        transaction
      })
    } catch (error) {
      console.log('UserLotteryLimit表不存在，跳过删除:', error.message)
    }

    // 删除所有用户（保留管理员）
    try {
      resetResults.users = await User.destroy({
        where: {},
        transaction
      })
    } catch (error) {
      console.log('User表不存在，跳过删除:', error.message)
    }

    // 重置抽奖期次状态
    try {
      const [affectedRows] = await LotteryPeriod.update(
        {
          status: '未开始',
          totalParticipants: 0,
          totalAttempts: 0
        },
        {
          where: {},
          transaction
        }
      )
      resetResults.periodsReset = affectedRows
    } catch (error) {
      console.log('LotteryPeriod表不存在，跳过重置:', error.message)
    }

    // 重置奖品配置的中奖人数
    try {
      const [affectedRows] = await PrizeConfig.update(
        { currentWinners: 0 },
        {
          where: {},
          transaction
        }
      )
      resetResults.prizesReset = affectedRows
    } catch (error) {
      console.log('PrizeConfig表不存在，跳过重置:', error.message)
    }

    await transaction.commit()

    const message = `数据重置完成 - 删除抽奖记录: ${resetResults.lotteryRecords}条, 删除用户: ${resetResults.users}条, 重置期次: ${resetResults.periodsReset}个, 重置奖品: ${resetResults.prizesReset}个`

    // 记录操作日志
    await createAdminLog(
      req.admin.id,
      '数据管理',
      '重置所有数据',
      '高',
      {
        resetResults: resetResults,
        message: message,
        timestamp: new Date().toISOString()
      },
      req
    )

    res.json({
      success: true,
      message,
      data: resetResults
    })
  } catch (error) {
    await transaction.rollback()
    console.error('重置数据失败:', error)
    res.status(500).json({
      success: false,
      error: '重置数据失败: ' + error.message
    })
  }
})

/**
 * 获取管理员操作日志
 * GET /api/admin/logs
 */
router.get('/logs', authenticateAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      action = '',
      date = '',
      riskLevel = ''
    } = req.query

    // 构建查询条件
    const whereConditions = {}

    // 操作类型筛选
    if (action) {
      whereConditions.action = action
    }

    // 风险等级筛选
    if (riskLevel) {
      whereConditions.riskLevel = riskLevel
    }

    // 日期筛选
    if (date) {
      const startDate = new Date(date)
      const endDate = new Date(date)
      endDate.setDate(endDate.getDate() + 1)

      whereConditions.createdAt = {
        [Op.gte]: startDate,
        [Op.lt]: endDate
      }
    }

    // 管理员搜索条件
    const includeConditions = {
      model: Admin,
      as: 'admin',
      attributes: ['id', 'username', 'email', 'role'],
      required: false
    }

    if (search) {
      includeConditions.where = {
        username: {
          [Op.like]: `%${search}%`
        }
      }
    }

    // 分页参数
    const offset = (parseInt(page) - 1) * parseInt(limit)
    const pageLimit = parseInt(limit)

    // 查询日志
    const { count, rows: logs } = await AdminLog.findAndCountAll({
      where: whereConditions,
      include: [includeConditions],
      order: [['createdAt', 'DESC']],
      limit: pageLimit,
      offset: offset
    })

    // 计算分页信息
    const totalPages = Math.ceil(count / pageLimit)

    res.json({
      success: true,
      message: '获取管理员日志成功',
      data: {
        logs,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount: count,
          pageSize: pageLimit,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      }
    })
  } catch (error) {
    console.error('获取管理员日志失败:', error)
    res.status(500).json({
      success: false,
      error: '获取管理员日志失败: ' + error.message
    })
  }
})

/**
 * 获取管理员日志详情
 * GET /api/admin/logs/:id
 */
router.get('/logs/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params

    const log = await AdminLog.findByPk(id, {
      include: [
        {
          model: Admin,
          as: 'admin',
          attributes: ['id', 'username', 'email', 'role']
        }
      ]
    })

    if (!log) {
      return res.status(404).json({
        success: false,
        error: '日志记录不存在'
      })
    }

    res.json({
      success: true,
      message: '获取日志详情成功',
      data: log
    })
  } catch (error) {
    console.error('获取日志详情失败:', error)
    res.status(500).json({
      success: false,
      error: '获取日志详情失败: ' + error.message
    })
  }
})

/**
 * 发放奖品
 * PUT /api/admin/lottery-records/:id/distribute
 */
router.put('/lottery-records/:id/distribute', authenticateAdmin, async (req, res) => {
  const transaction = await sequelize.transaction()

  try {
    const recordId = req.params.id
    const { remark = '' } = req.body

    // 查找抽奖记录
    const record = await LotteryRecord.findByPk(recordId, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'platformId', 'totalWinnings']
        },
        {
          model: LotteryPeriod,
          as: 'period',
          attributes: ['id', 'periodName']
        }
      ],
      transaction
    })

    if (!record) {
      await transaction.rollback()
      return res.status(404).json({
        success: false,
        error: '抽奖记录不存在'
      })
    }

    // 检查是否为中奖记录
    if (!record.isWinner) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '只能发放中奖记录的奖品'
      })
    }

    // 检查是否已经发放
    if (record.distributedAt) {
      await transaction.rollback()
      return res.status(400).json({
        success: false,
        error: '该奖品已经发放过了'
      })
    }

    // 更新抽奖记录
    await record.update({
      distributedAt: new Date(),
      distributedBy: req.admin.id,
      distributionRemark: remark,
      status: '已发放'
    }, { transaction })

    // 更新用户总奖金
    const oldTotalWinnings = record.user?.totalWinnings || 0
    if (record.user) {
      await record.user.update({
        totalWinnings: oldTotalWinnings + record.prizeAmount
      }, { transaction })
    }

    // 记录管理员操作日志
    await createAdminLog(
      req.admin.id,
      '奖品发放',
      `发放奖品给用户 ${record.user?.platformId || '未知用户'}: ${record.prizeName} (¥${record.prizeAmount})`,
      '中', // 奖品发放属于中等风险操作
      {
        recordId: record.id,
        userId: record.user?.id,
        userPlatformId: record.user?.platformId,
        userName: record.user?.username,
        periodId: record.period?.id,
        periodName: record.period?.periodName,
        prizeName: record.prizeName,
        prizeAmount: record.prizeAmount,
        distributionRemark: remark,
        oldTotalWinnings: oldTotalWinnings,
        newTotalWinnings: oldTotalWinnings + record.prizeAmount,
        distributedAt: new Date().toISOString()
      },
      req
    )

    await transaction.commit()

    res.json({
      success: true,
      message: '奖品发放成功',
      data: {
        recordId: record.id,
        prizeName: record.prizeName,
        prizeAmount: record.prizeAmount,
        distributedAt: record.distributedAt,
        distributedBy: req.admin.id,
        distributionRemark: remark
      }
    })

  } catch (error) {
    await transaction.rollback()
    console.error('发放奖品错误:', error)
    res.status(500).json({
      success: false,
      error: '发放奖品失败'
    })
  }
})

/**
 * 获取用户操作日志
 * GET /api/admin/user-logs
 */
router.get('/user-logs', authenticateAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      action = '',
      date = ''
    } = req.query

    // 构建查询条件
    const whereConditions = {}

    // 操作类型筛选
    if (action) {
      whereConditions.action = action
    }

    // 日期筛选
    if (date) {
      const startDate = new Date(date)
      const endDate = new Date(date)
      endDate.setDate(endDate.getDate() + 1)

      whereConditions.createdAt = {
        [Op.gte]: startDate,
        [Op.lt]: endDate
      }
    }

    // 用户搜索条件
    const includeConditions = {
      model: User,
      as: 'user',
      attributes: ['id', 'username', 'platformId', 'status'],
      required: false
    }

    if (search) {
      includeConditions.where = {
        [Op.or]: [
          { username: { [Op.like]: `%${search}%` } },
          { platformId: { [Op.like]: `%${search}%` } }
        ]
      }
    }

    // 分页参数
    const offset = (parseInt(page) - 1) * parseInt(limit)
    const pageLimit = parseInt(limit)

    // 查询日志
    const { count, rows: logs } = await UserLog.findAndCountAll({
      where: whereConditions,
      include: [includeConditions],
      order: [['createdAt', 'DESC']],
      limit: pageLimit,
      offset: offset
    })

    // 计算分页信息
    const totalPages = Math.ceil(count / pageLimit)

    res.json({
      success: true,
      message: '获取用户日志成功',
      data: {
        logs,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount: count,
          pageSize: pageLimit,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      }
    })
  } catch (error) {
    console.error('获取用户日志失败:', error)
    res.status(500).json({
      success: false,
      error: '获取用户日志失败: ' + error.message
    })
  }
})

/**
 * 获取用户日志详情
 * GET /api/admin/user-logs/:id
 */
router.get('/user-logs/:id', authenticateAdmin, async (req, res) => {
  try {
    const { id } = req.params

    const log = await UserLog.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'platformId', 'status']
        }
      ]
    })

    if (!log) {
      return res.status(404).json({
        success: false,
        error: '日志记录不存在'
      })
    }

    res.json({
      success: true,
      message: '获取日志详情成功',
      data: log
    })
  } catch (error) {
    console.error('获取用户日志详情失败:', error)
    res.status(500).json({
      success: false,
      error: '获取用户日志详情失败: ' + error.message
    })
  }
})

module.exports = router
