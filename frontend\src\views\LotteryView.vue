<template>
  <div class="min-h-screen bg-black text-white">
    <!-- 使用公共头部组件 -->
    <AppHeader />


    <!-- 主要内容区域 -->
    <main class="min-h-screen relative overflow-hidden">
      <!-- 动态背景层 -->
      <div class="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <!-- 动态光球 -->
        <div class="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-pink-500/15 to-red-500/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-gold-500/10 to-yellow-500/10 rounded-full blur-3xl animate-pulse delay-2000"></div>

        <!-- 星光效果 -->
        <div class="absolute top-1/4 left-1/3 w-2 h-2 bg-white rounded-full animate-ping"></div>
        <div class="absolute top-3/4 left-2/3 w-1 h-1 bg-gold-400 rounded-full animate-ping delay-500"></div>
        <div class="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping delay-1000"></div>
      </div>

      <!-- Hero Section -->
      <section class="relative z-10 py-20 px-4 text-center">
        <div class="max-w-6xl mx-auto">
          <!-- 主标题 -->
          <div class="mb-12">
            <h1 class="text-7xl md:text-8xl font-black mb-8 tracking-tight">
              <span class="bg-gradient-to-r from-gold-300 via-gold-400 to-gold-500 bg-clip-text text-transparent drop-shadow-2xl">
                幸运转盘
              </span>
            </h1>
            <p class="text-xl md:text-2xl text-gray-300 font-light max-w-2xl mx-auto leading-relaxed">
              每一次转动，都是一次全新的机遇
            </p>

            <!-- 风险提示 -->
            <div class="mt-8 max-w-5xl mx-auto px-4">
              <div class="relative bg-gradient-to-r from-red-900/40 via-red-800/30 to-red-900/40 border-2 border-red-400/60 rounded-2xl p-6 backdrop-blur-md shadow-2xl">
                <!-- 装饰性边框动画 -->
                <div class="absolute inset-0 bg-gradient-to-r from-red-500/20 via-transparent to-red-500/20 rounded-2xl animate-pulse"></div>

                <!-- 内容区域 -->
                <div class="relative z-10">
                  <!-- 标题区域 -->
                  <div class="flex items-center justify-center mb-4">
                    <div class="flex items-center space-x-3">
                      <div class="text-3xl animate-bounce">⚠️</div>
                      <h3 class="text-red-300 font-bold text-lg md:text-xl tracking-wide">重要风险提示</h3>
                      <div class="text-3xl animate-bounce delay-300">⚠️</div>
                    </div>
                  </div>

                  <!-- 分割线 -->
                  <div class="w-full h-px bg-gradient-to-r from-transparent via-red-400/50 to-transparent mb-4"></div>

                  <!-- 提示内容 -->
                  <div class="text-center">
                    <p class="text-red-100 text-sm md:text-base leading-relaxed font-medium">
                      <span class="text-red-200 font-bold">禁止一人或团队等使用多个账号参与抽奖</span>，
                      <span class="text-yellow-200">涉嫌套利等行为</span>，
                      <span class="text-red-300 font-bold">查实直接封禁在本平台所有账户</span>，
                      <span class="text-gray-300 text-sm">最终解释权归本公司所有</span>。
                    </p>
                  </div>

                  <!-- 底部装饰 -->
                  <div class="flex justify-center mt-4 space-x-2">
                    <div class="w-2 h-2 bg-red-400 rounded-full animate-ping"></div>
                    <div class="w-2 h-2 bg-red-400 rounded-full animate-ping delay-150"></div>
                    <div class="w-2 h-2 bg-red-400 rounded-full animate-ping delay-300"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 期数信息卡片 - 响应式设计 -->
          <div v-if="hasActiveActivity && currentPeriod" class="w-full max-w-6xl mx-auto mb-8 px-4">
            <div class="bg-white/10 backdrop-blur-md border border-white/20 rounded-3xl shadow-2xl overflow-hidden">

              <!-- 桌面端布局 (md及以上) -->
              <div class="hidden md:block p-8">
                <div class="flex items-center justify-center space-x-12">
                  <div class="text-center">
                    <div class="text-sm text-gray-400 mb-2 font-medium">当前期数</div>
                    <div class="text-4xl font-bold text-gold-400">{{ currentPeriod.periodName }}</div>
                  </div>
                  <div class="w-px h-16 bg-gradient-to-b from-transparent via-white/30 to-transparent"></div>
                  <div class="text-center">
                    <div class="text-sm text-gray-400 mb-2 font-medium">奖池金额</div>
                    <div class="text-3xl font-bold text-green-400">¥{{ totalPrizePool.toLocaleString() }}</div>
                  </div>
                  <div class="w-px h-16 bg-gradient-to-b from-transparent via-white/30 to-transparent"></div>
                  <div class="text-center">
                    <div class="text-sm text-gray-400 mb-2 font-medium">截止时间</div>
                    <div class="text-xl font-bold" :class="timeRemaining.isUrgent ? 'text-red-400 animate-pulse' : 'text-blue-400'">
                      <div class="flex flex-col items-center">
                        <div class="text-xs text-gray-500 mb-1">{{ formatEndTime(currentPeriod.endTime) }}</div>
                        <div class="flex items-center space-x-1">
                          <span class="text-lg">⏰</span>
                          <span>{{ timeRemaining.display }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="w-px h-16 bg-gradient-to-b from-transparent via-white/30 to-transparent"></div>
                  <div class="text-center">
                    <div class="text-sm text-gray-400 mb-2 font-medium">活动状态</div>
                    <div class="text-2xl font-bold" :class="currentPeriod.status === '进行中' ? 'text-green-400' : 'text-yellow-400'">
                      <span class="inline-flex items-center">
                        <span class="w-3 h-3 rounded-full mr-2" :class="currentPeriod.status === '进行中' ? 'bg-green-400 animate-pulse' : 'bg-yellow-400'"></span>
                        {{ currentPeriod.status }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 移动端布局 (md以下) -->
              <div class="md:hidden p-6">
                <!-- 第一行：期数和状态 -->
                <div class="flex items-center justify-between mb-6">
                  <div class="text-center">
                    <div class="text-xs text-gray-400 mb-1 font-medium">当前期数</div>
                    <div class="text-2xl font-bold text-gold-400">第{{ currentPeriod.periodNumber }}期</div>
                  </div>
                  <div class="text-center">
                    <div class="text-xs text-gray-400 mb-1 font-medium">活动状态</div>
                    <div class="text-lg font-bold" :class="currentPeriod.status === '进行中' ? 'text-green-400' : 'text-yellow-400'">
                      <span class="inline-flex items-center">
                        <span class="w-2 h-2 rounded-full mr-1" :class="currentPeriod.status === '进行中' ? 'bg-green-400 animate-pulse' : 'bg-yellow-400'"></span>
                        {{ currentPeriod.status }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 分隔线 -->
                <div class="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent mb-6"></div>

                <!-- 第二行：奖池金额 -->
                <div class="text-center mb-6">
                  <div class="text-xs text-gray-400 mb-2 font-medium">奖池金额</div>
                  <div class="text-3xl font-bold text-green-400">¥{{ totalPrizePool.toLocaleString() }}</div>
                </div>

                <!-- 分隔线 -->
                <div class="h-px bg-gradient-to-r from-transparent via-white/30 to-transparent mb-6"></div>

                <!-- 第三行：截止时间 -->
                <div class="text-center">
                  <div class="text-xs text-gray-400 mb-2 font-medium">截止时间</div>
                  <div class="text-lg font-bold" :class="timeRemaining.isUrgent ? 'text-red-400 animate-pulse' : 'text-blue-400'">
                    <div class="flex flex-col items-center space-y-1">
                      <div class="text-xs text-gray-500">{{ formatEndTime(currentPeriod.endTime) }}</div>
                      <div class="flex items-center space-x-1">
                        <span class="text-base">⏰</span>
                        <span class="text-lg">{{ timeRemaining.display }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 没有活动状态 -->
      <section v-if="!hasActiveActivity" class="relative z-10 px-4 pb-20">
        <div class="w-full max-w-4xl mx-auto">
          <div class="bg-gradient-to-r from-gray-800/60 via-gray-700/60 to-gray-800/60 backdrop-blur-md border-2 border-gray-500/60 rounded-3xl shadow-2xl overflow-hidden">
            <div class="p-12 text-center">
              <!-- 图标 -->
              <div class="mb-6">
                <div class="inline-flex items-center justify-center w-24 h-24 bg-gray-600/30 rounded-full">
                  <span class="text-5xl">🎯</span>
                </div>
              </div>

              <!-- 主要信息 -->
              <h3 class="text-3xl font-bold text-gray-300 mb-4">当前暂无抽奖活动</h3>
              <p class="text-lg text-gray-400 mb-8 max-w-2xl mx-auto leading-relaxed">
                抽奖活动暂时停止，请关注最新活动通知。我们会尽快为您带来更多精彩的抽奖活动！
              </p>

              <!-- 装饰性元素 -->
              <div class="flex justify-center space-x-4 opacity-50">
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-300"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-700"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 主转盘区域 -->
      <section v-if="hasActiveActivity" class="relative z-10 px-4 pb-20">
        <div v-if="isLoading" class="text-center py-32">
          <div class="inline-block">
            <div class="w-32 h-32 border-8 border-gold-400/30 border-t-gold-400 rounded-full animate-spin mb-8"></div>
            <div class="text-gold-400 text-3xl font-bold">加载中...</div>
          </div>
        </div>
        <div v-else class="max-w-7xl mx-auto">

          <!-- 转盘主体 -->
          <div class="flex justify-center mb-20">
            <div class="relative">
              <!-- Lucky Canvas 转盘 -->
              <div class="relative z-10 transform hover:scale-105 transition-transform duration-300">
                <LuckyWheel
                  ref="myLucky"
                  :width="wheelSize"
                  :height="wheelSize"
                  :prizes="luckyPrizes"
                  :blocks="luckyBlocks"
                  :buttons="luckyButtons"
                  :default-config="luckyDefaultConfig"
                  :default-style="luckyDefaultStyle"
                  @start="startCallback"
                  @end="endCallback"
                />
              </div>
            </div>
          </div>

          <!-- 简洁状态指示器 -->
          <div class="max-w-6xl mx-auto mb-16">
            <!-- 状态提示条 - 未登录 -->
            <div v-if="!authStore.isAuthenticated" class="bg-gradient-to-r from-slate-800/80 to-slate-700/80 backdrop-blur-sm border border-slate-600/50 rounded-2xl p-6">
              <!-- 桌面端布局 -->
              <div class="hidden md:flex items-center justify-center space-x-4">
                <div class="text-3xl">🔐</div>
                <div class="text-white text-lg font-medium">请先登录参与抽奖</div>
                <button @click="goToLogin" class="px-8 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-black font-bold rounded-xl transition-all duration-300 transform hover:scale-105">
                  立即登录
                </button>
              </div>
              <!-- 移动端布局 -->
              <div class="md:hidden text-center space-y-4">
                <div class="flex items-center justify-center space-x-3">
                  <div class="text-2xl">🔐</div>
                  <div class="text-white text-base font-medium">请先登录参与抽奖</div>
                </div>
                <button @click="goToLogin" class="w-full py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-black font-bold rounded-xl transition-all duration-300">
                  立即登录
                </button>
              </div>
            </div>

            <!-- 状态提示条 - 次数用完 -->
            <div v-else-if="!userStatus || (!userStatus.remainingShares || userStatus.remainingShares <= 0) || (!userStatus.remainingDailyAttempts || userStatus.remainingDailyAttempts <= 0)" class="bg-gradient-to-r from-slate-800/80 to-slate-700/80 backdrop-blur-sm border border-slate-600/50 rounded-2xl p-6">
              <!-- 桌面端布局 -->
              <div class="hidden md:flex items-center justify-center space-x-4">
                <div class="text-3xl">⏰</div>
                <div class="text-white text-lg font-medium">
                  {{ (!userStatus?.remainingShares || userStatus.remainingShares <= 0) ? '本期抽奖份数已用完' : '今日抽奖次数已用完' }}
                </div>
                <button @click="openExternalLink('https://jspg7.com')" class="px-8 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400 text-black font-bold rounded-xl transition-all duration-300 transform hover:scale-105">
                  获得更多次数
                </button>
              </div>
              <!-- 移动端布局 -->
              <div class="md:hidden text-center space-y-4">
                <div class="flex items-center justify-center space-x-3">
                  <div class="text-2xl">⏰</div>
                  <div class="text-white text-base font-medium">本期抽奖次数已用完</div>
                </div>
                <button @click="openExternalLink('https://jspg7.com')" class="w-full py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400 text-black font-bold rounded-xl transition-all duration-300">
                  获得更多次数
                </button>
              </div>
            </div>

            <!-- 状态提示条 - 正常状态 -->
            <div v-else class="bg-gradient-to-r from-slate-800/80 to-slate-700/80 backdrop-blur-sm border border-slate-600/50 rounded-2xl p-6">
              <!-- 桌面端布局 -->
              <div class="hidden md:flex items-center justify-between">
                <div class="flex items-center space-x-8">
                  <div class="text-3xl">🎯</div>
                  <div class="text-white text-lg font-medium">点击转盘中心开始抽奖</div>

                  <!-- 简洁数据展示 -->
                  <div class="flex items-center space-x-4">
                    <div class="text-center">
                      <div class="text-xl font-bold text-gold-400">{{ authStore.isAuthenticated ? (userStatus?.remainingShares || 0) : (currentPeriod?.totalShares || 0) }}</div>
                      <div class="text-gray-400 text-xs">剩余份数</div>
                    </div>
                    <div class="text-center">
                      <div class="text-xl font-bold text-blue-400">{{ authStore.isAuthenticated ? (userStatus?.remainingDailyAttempts || 0) : (currentPeriod?.maxAttemptsPerUser || 0) }}</div>
                      <div class="text-gray-400 text-xs">今日剩余</div>
                    </div>
                    <div class="text-center">
                      <div class="text-xl font-bold text-green-400">¥{{ userStatus?.user?.totalWinnings?.toFixed(2) || '0.00' }}</div>
                      <div class="text-gray-400 text-xs">累计中奖</div>
                    </div>
                  </div>
                </div>

                <button @click="openExternalLink('https://jspg7.com')" class="px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-400 hover:to-emerald-400 text-black font-bold rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                  <span class="text-lg">💎</span>
                  <span>获得更多次数</span>
                </button>
              </div>

              <!-- 移动端布局 -->
              <div class="md:hidden space-y-4">
                <!-- 提示信息 -->
                <div class="flex items-center justify-center space-x-3">
                  <div class="text-2xl">🎯</div>
                  <div class="text-white text-base font-medium">点击转盘中心开始抽奖</div>
                </div>

                <!-- 数据展示 -->
                <div class="flex items-center justify-center space-x-6">
                  <div class="text-center">
                    <div class="text-lg font-bold text-gold-400">{{ authStore.isAuthenticated ? (userStatus?.remainingShares || 0) : (currentPeriod?.totalShares || 0) }}</div>
                    <div class="text-gray-400 text-xs">剩余份数</div>
                  </div>
                  <div class="text-center">
                    <div class="text-lg font-bold text-blue-400">{{ authStore.isAuthenticated ? (userStatus?.remainingDailyAttempts || 0) : (currentPeriod?.maxAttemptsPerUser || 0) }}</div>
                    <div class="text-gray-400 text-xs">今日剩余</div>
                  </div>
                  <div class="text-center">
                    <div class="text-lg font-bold text-green-400">¥{{ userStatus?.user?.totalWinnings?.toFixed(2) || '0.00' }}</div>
                    <div class="text-gray-400 text-xs">累计中奖</div>
                  </div>
                </div>

                <!-- 按钮 -->
                <button @click="openExternalLink('https://jspg7.com')" class="w-full py-3 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-400 hover:to-emerald-400 text-black font-bold rounded-xl transition-all duration-300 flex items-center justify-center space-x-2">
                  <span class="text-lg">💎</span>
                  <span>获得更多次数</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 简洁信息展示区域 - 响应式布局 -->
          <div class="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 px-4">

            <!-- 左侧：奖品与公告 -->
            <div class="space-y-8">
              <!-- 奖品列表 -->
              <div class="bg-gradient-to-br from-slate-800/80 to-slate-700/80 backdrop-blur-sm border border-slate-600/50 rounded-2xl p-6">
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center space-x-3">
                    <div class="text-2xl">🎁</div>
                    <h3 class="text-xl font-bold text-white">奖品一览</h3>
                  </div>
                  <div class="text-sm text-gray-400">共{{ (prizes || []).length }}个奖品</div>
                </div>
                <div class="marquee-container">
                  <div class="marquee-content">
                    <!-- 第一组内容 -->
                    <div class="marquee-group">
                      <div
                        v-for="(prize, index) in (prizes || [])"
                        :key="`${prize?.id || index}`"
                        class="flex items-center justify-between p-4 bg-slate-700/50 rounded-xl hover:bg-slate-600/50 transition-all duration-200 border border-slate-600/30 mb-3"
                      >
                        <div class="flex items-center space-x-3">
                          <div class="w-8 h-8 rounded-full bg-gradient-to-r from-gold-400 to-gold-500 flex items-center justify-center text-black font-bold text-sm">
                            {{ index + 1 }}
                          </div>
                          <span class="text-white font-medium">{{ prize?.prizeName || '未知奖品' }}</span>
                        </div>
                        <span class="text-gold-400 font-bold">
                          {{ (prize?.prizeAmount || 0) > 0 ? `¥${(prize?.prizeAmount || 0).toLocaleString()}` : '谢谢参与' }}
                        </span>
                      </div>

                      <!-- 测试数据：确保有足够内容进行滚动 -->
                      <div v-if="(prizes || []).length < 6">
                        <div
                          v-for="i in (6 - (prizes || []).length)"
                          :key="`test-${i}`"
                          class="flex items-center justify-between p-4 bg-slate-700/30 rounded-xl border border-slate-600/20 opacity-60 mb-3"
                        >
                          <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-full bg-gradient-to-r from-gray-400 to-gray-500 flex items-center justify-center text-black font-bold text-sm">
                              {{ (prizes || []).length + i }}
                            </div>
                            <span class="text-gray-300 font-medium">测试奖品 {{ i }}</span>
                          </div>
                          <span class="text-gray-400 font-bold">¥{{ (i * 100).toLocaleString() }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- 第二组内容（重复第一组，实现无缝循环） -->
                    <div class="marquee-group">
                      <div
                        v-for="(prize, index) in (prizes || [])"
                        :key="`dup-${prize?.id || index}`"
                        class="flex items-center justify-between p-4 bg-slate-700/50 rounded-xl hover:bg-slate-600/50 transition-all duration-200 border border-slate-600/30 mb-3"
                      >
                        <div class="flex items-center space-x-3">
                          <div class="w-8 h-8 rounded-full bg-gradient-to-r from-gold-400 to-gold-500 flex items-center justify-center text-black font-bold text-sm">
                            {{ index + 1 }}
                          </div>
                          <span class="text-white font-medium">{{ prize?.prizeName || '未知奖品' }}</span>
                        </div>
                        <span class="text-gold-400 font-bold">
                          {{ (prize?.prizeAmount || 0) > 0 ? `¥${(prize?.prizeAmount || 0).toLocaleString()}` : '谢谢参与' }}
                        </span>
                      </div>

                      <!-- 测试数据重复 -->
                      <div v-if="(prizes || []).length < 6">
                        <div
                          v-for="i in (6 - (prizes || []).length)"
                          :key="`test-dup-${i}`"
                          class="flex items-center justify-between p-4 bg-slate-700/30 rounded-xl border border-slate-600/20 opacity-60 mb-3"
                        >
                          <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-full bg-gradient-to-r from-gray-400 to-gray-500 flex items-center justify-center text-black font-bold text-sm">
                              {{ (prizes || []).length + i }}
                            </div>
                            <span class="text-gray-300 font-medium">测试奖品 {{ i }}</span>
                          </div>
                          <span class="text-gray-400 font-bold">¥{{ (i * 100).toLocaleString() }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 中奖公告 -->
              <div class="bg-gradient-to-br from-slate-800/80 to-slate-700/80 backdrop-blur-sm border border-slate-600/50 rounded-2xl p-6">
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center space-x-3">
                    <div class="text-2xl">🏆</div>
                    <h3 class="text-xl font-bold text-white">中奖公告</h3>
                  </div>
                  <span class="text-xs text-green-400 animate-pulse bg-green-500/20 px-3 py-1 rounded-full">实时更新</span>
                </div>
                <div class="marquee-container">
                  <div class="marquee-content">
                    <!-- 第一组内容 -->
                    <div class="marquee-group">
                      <div
                        v-for="announcement in (winningAnnouncements || [])"
                        :key="`${announcement?.id || Math.random()}`"
                        class="p-4 bg-slate-700/50 rounded-xl hover:bg-slate-600/50 transition-all duration-200 border border-slate-600/30 mb-3"
                      >
                        <div class="flex items-center justify-between mb-3">
                          <span class="text-gold-400 font-bold">用户***{{ announcement.userId }}</span>
                          <span class="text-xs text-gray-400">{{ announcement.timeAgo }}分钟前</span>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-gray-300">获得</span>
                          <span class="text-green-400 font-bold">
                            {{ announcement.prizeName }}
                            <span v-if="announcement.prizeAmount > 0" class="text-gold-400 ml-2">¥{{ announcement.prizeAmount.toLocaleString() }}</span>
                          </span>
                        </div>
                      </div>

                      <!-- 测试数据：模拟中奖公告 -->
                      <div v-if="(!winningAnnouncements || winningAnnouncements.length === 0)">
                        <div
                          v-for="i in 5"
                          :key="`test-announcement-${i}`"
                          class="p-4 bg-slate-700/30 rounded-xl hover:bg-slate-600/30 transition-all duration-200 border border-slate-600/20 opacity-60 mb-3"
                        >
                          <div class="flex items-center justify-between mb-3">
                            <span class="text-gold-300 font-bold">{{ generateRandomUsername(i) }}</span>
                            <span class="text-xs text-gray-500">{{ generateRandomTime(i) }}分钟前</span>
                          </div>
                          <div class="flex items-center justify-between">
                            <span class="text-gray-400">获得</span>
                            <span class="text-green-300 font-bold">
                              {{ generateRandomPrize(i).name }}
                              <span v-if="generateRandomPrize(i).amount > 0" class="text-gold-300 ml-2">¥{{ generateRandomPrize(i).amount.toLocaleString() }}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 第二组内容（重复第一组，实现无缝循环） -->
                    <div class="marquee-group">
                      <div
                        v-for="announcement in (winningAnnouncements || [])"
                        :key="`dup-${announcement?.id || Math.random()}`"
                        class="p-4 bg-slate-700/50 rounded-xl hover:bg-slate-600/50 transition-all duration-200 border border-slate-600/30 mb-3"
                      >
                        <div class="flex items-center justify-between mb-3">
                          <span class="text-gold-400 font-bold">用户***{{ announcement.userId }}</span>
                          <span class="text-xs text-gray-400">{{ announcement.timeAgo }}分钟前</span>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-gray-300">获得</span>
                          <span class="text-green-400 font-bold">
                            {{ announcement.prizeName }}
                            <span v-if="announcement.prizeAmount > 0" class="text-gold-400 ml-2">¥{{ announcement.prizeAmount.toLocaleString() }}</span>
                          </span>
                        </div>
                      </div>

                      <!-- 测试数据重复 -->
                      <div v-if="(!winningAnnouncements || winningAnnouncements.length === 0)">
                        <div
                          v-for="i in 5"
                          :key="`test-announcement-dup-${i}`"
                          class="p-4 bg-slate-700/30 rounded-xl hover:bg-slate-600/30 transition-all duration-200 border border-slate-600/20 opacity-60 mb-3"
                        >
                          <div class="flex items-center justify-between mb-3">
                            <span class="text-gold-300 font-bold">{{ generateRandomUsername(i + 10) }}</span>
                            <span class="text-xs text-gray-500">{{ generateRandomTime(i + 10) }}分钟前</span>
                          </div>
                          <div class="flex items-center justify-between">
                            <span class="text-gray-400">获得</span>
                            <span class="text-green-300 font-bold">
                              {{ generateRandomPrize(i + 10).name }}
                              <span v-if="generateRandomPrize(i + 10).amount > 0" class="text-gold-300 ml-2">¥{{ generateRandomPrize(i + 10).amount.toLocaleString() }}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：规则与统计 -->
            <div class="space-y-8">
              <!-- 抽奖规则 -->
              <div class="bg-gradient-to-br from-slate-800/80 to-slate-700/80 backdrop-blur-sm border border-slate-600/50 rounded-2xl p-6">
                <div class="flex items-center space-x-3 mb-6">
                  <div class="text-2xl">📋</div>
                  <h3 class="text-xl font-bold text-white">抽奖规则</h3>
                </div>
                <div class="space-y-4">
                  <div class="flex items-start space-x-4 p-3 bg-slate-700/30 rounded-xl">
                    <div class="text-xl">🎯</div>
                    <div>
                      <div class="text-white font-semibold mb-1">免费抽奖</div>
                      <div class="text-gray-400 text-sm">每期免费{{ currentPeriod?.maxAttemptsPerUser || 1 }}次机会</div>
                    </div>
                  </div>
                  <div class="flex items-start space-x-4 p-3 bg-slate-700/30 rounded-xl">
                    <div class="text-xl">💰</div>
                    <div>
                      <div class="text-white font-semibold mb-1">即时到账</div>
                      <div class="text-gray-400 text-sm">中奖金额自动添加到账户</div>
                    </div>
                  </div>
                  <div class="flex items-start space-x-4 p-3 bg-slate-700/30 rounded-xl">
                    <div class="text-xl">🔒</div>
                    <div>
                      <div class="text-white font-semibold mb-1">公平公正</div>
                      <div class="text-gray-400 text-sm">所有结果真实有效</div>
                    </div>
                  </div>
                  <div class="flex items-start space-x-4 p-3 bg-slate-700/30 rounded-xl">
                    <div class="text-xl">⚡</div>
                    <div>
                      <div class="text-white font-semibold mb-1">实时开奖</div>
                      <div class="text-gray-400 text-sm">转盘停止即可知晓结果</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 活动统计 -->
              <div class="bg-gradient-to-br from-slate-800/80 to-slate-700/80 backdrop-blur-sm border border-slate-600/50 rounded-2xl p-6">
                <div class="flex items-center space-x-3 mb-6">
                  <div class="text-2xl">📊</div>
                  <h3 class="text-xl font-bold text-white">活动统计</h3>
                </div>
                <div class="grid grid-cols-2 gap-4">
                  <div class="text-center p-4 bg-slate-700/30 rounded-xl">
                    <div class="text-2xl font-bold text-blue-400 mb-1">{{ currentPeriod?.maxAttemptsPerUser || 1 }}</div>
                    <div class="text-gray-400 text-sm">每日次数</div>
                  </div>
                  <div class="text-center p-4 bg-slate-700/30 rounded-xl">
                    <div class="text-2xl font-bold text-purple-400 mb-1">{{ currentPeriod?.totalShares || 1 }}</div>
                    <div class="text-gray-400 text-sm">总份数</div>
                  </div>
                  <div class="text-center p-4 bg-slate-700/30 rounded-xl">
                    <div class="text-2xl font-bold text-green-400 mb-1">{{ (prizes || []).filter(p => p.prizeAmount > 0).length }}</div>
                    <div class="text-gray-400 text-sm">现金奖品</div>
                  </div>
                  <div class="text-center p-4 bg-slate-700/30 rounded-xl">
                    <div class="text-2xl font-bold text-orange-400 mb-1">{{ (prizes || []).length }}</div>
                    <div class="text-gray-400 text-sm">总奖品数</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


    </main>

      <!-- 抽奖结果弹窗 -->
      <div v-if="showLotteryResult" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50" @click="showLotteryResult = false">
        <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl p-8 border-2 border-gold-500 max-w-md w-full mx-4 relative" @click.stop>
          <button @click="showLotteryResult = false" class="absolute top-4 right-4 text-gray-400 hover:text-white">
            <span class="text-2xl">×</span>
          </button>

          <div class="text-center">
            <div class="text-6xl mb-4">🎉</div>
            <h3 class="text-3xl font-bold text-gold-400 mb-4">抽奖结果</h3>

            <!-- 错误信息显示 -->
            <div v-if="lotteryResult?.isError" class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl p-6 mb-6">
              <div class="text-white text-xl font-bold">{{ lotteryResult.errorMessage }}</div>
            </div>

            <!-- 正常抽奖结果显示 -->
            <div v-else-if="lotteryResult" class="bg-gradient-to-r from-gold-500 to-gold-600 rounded-xl p-6 mb-6">
              <div class="text-black text-2xl font-bold mb-2">{{ lotteryResult.prizeName }}</div>
              <div v-if="lotteryResult.prizeAmount && lotteryResult.prizeAmount > 0" class="text-black text-xl">
                恭喜您获得 ¥{{ lotteryResult.prizeAmount }}
              </div>
              <div v-else class="text-black text-lg">
                谢谢参与，下次再来！
              </div>
            </div>

            <div v-if="lotteryResult && !lotteryResult.isError" class="text-gray-300 mb-6">
              剩余抽奖次数: <span class="text-gold-400 font-bold">{{ lotteryResult.remainingDailyAttempts }}</span>
            </div>

            <button
              @click="showLotteryResult = false"
              class="w-full bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-black font-bold py-3 rounded-lg transition-all duration-300"
            >
              确定
            </button>
          </div>
        </div>
      </div>

    <!-- 使用公共页脚组件 -->
    <AppFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { userAPI } from '../api/config'
import { useAuthStore } from '../stores/auth'
import AppHeader from '@/components/AppHeader.vue'
import AppFooter from '@/components/AppFooter.vue'
import axios, { AxiosError } from 'axios'

// 导入转盘图片资源
import wheelBgImg from '@/assets/lottery/转盘一体图.png'
import startBtnImg from '@/assets/lottery/<EMAIL>'

const router = useRouter()
const authStore = useAuthStore()

// 导航函数
const goToLogin = () => {
  router.push('/login')
}

// 打开外部链接
const openExternalLink = (url: string) => {
  window.open(url, '_blank')
}



// 响应式转盘尺寸
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 768)

const wheelSize = computed(() => {
  if (windowWidth.value < 768) { // 移动端
    return Math.min(windowWidth.value - 60, 350) + 'px'
  } else if (windowWidth.value < 1024) { // 平板
    return '420px'
  } else { // 桌面端
    return '600px' // 增加桌面端转盘尺寸
  }
})

// 窗口大小变化监听
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

// 生成随机用户名（使用index作为种子，确保相同index生成相同用户名）
const generateRandomUsername = (index: number) => {
  const prefixes = ['幸运', '开心', '快乐', '好运', '财富', '成功', '梦想', '希望', '阳光', '星光', '智慧', '勇敢', '温暖', '闪亮']
  const suffixes = ['用户', '玩家', '达人', '高手', '大神', '专家', '爱好者', '粉丝', '朋友', '伙伴', '小哥', '小姐', '先生', '女士']

  // 使用index作为种子生成固定的随机数
  const seed = index * 1234567 + 987654321
  const numbers = (seed % 9000) + 1000 // 1000-9999
  const prefix = prefixes[index % prefixes.length]
  const suffix = suffixes[(index + 7) % suffixes.length]

  // 生成脱敏用户名
  const fullName = `${prefix}${suffix}`
  const maskedName = fullName.substring(0, 2) + '***' + (numbers % 1000)
  return maskedName
}

// 生成随机奖品名称（使用接口数据）
const generateRandomPrize = (index: number) => {
  // 如果没有奖品数据，返回默认值
  if (!prizes.value || prizes.value.length === 0) {
    return { name: '谢谢参与', amount: 0 }
  }

  // 使用接口获取的奖品数据
  const prize = prizes.value[index % prizes.value.length]

  return {
    name: prize.prizeName || '未知奖品',
    amount: prize.prizeAmount || 0
  }
}

// 生成随机时间（分钟前）
const generateRandomTime = (index: number) => {
  const times = [1, 2, 3, 5, 8, 10, 15, 20, 25, 30, 45, 60, 90, 120]
  return times[index % times.length]
}

// 格式化结束时间显示
const formatEndTime = (endTime: string) => {
  if (!endTime) return ''
  const date = new Date(endTime)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${month}/${day} ${hours}:${minutes}`
}

// 计算剩余时间
const timeRemaining = ref({ display: '', isUrgent: false })

const updateTimeRemaining = () => {
  if (!currentPeriod.value?.endTime) {
    timeRemaining.value = { display: '未知', isUrgent: false }
    return
  }

  const now = new Date().getTime()
  const endTime = new Date(currentPeriod.value.endTime).getTime()
  const diff = endTime - now

  if (diff <= 0) {
    timeRemaining.value = { display: '已结束', isUrgent: true }
    return
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)

  let display = ''
  let isUrgent = false

  if (days > 0) {
    display = `${days}天${hours}小时`
  } else if (hours > 0) {
    display = `${hours}小时${minutes}分钟`
    isUrgent = hours < 2 // 少于2小时显示紧急状态
  } else if (minutes > 0) {
    display = `${minutes}分${seconds}秒`
    isUrgent = true
  } else {
    display = `${seconds}秒`
    isUrgent = true
  }

  timeRemaining.value = { display, isUrgent }
}

// 响应式数据
const isSpinning = ref(false)
const showLotteryResult = ref(false)
const lotteryResult = ref<{
  prizeName?: string
  prizeAmount?: number
  isWinner?: boolean
  configSource?: string
  remainingAttempts?: number
  remainingShares?: number
  remainingDailyAttempts?: number
  isError?: boolean
  errorMessage?: string
} | null>(null)

// 奖品配置数据（从API获取）
const prizes = ref<any[]>([])

// Lucky Canvas 转盘配置 - 使用一体图背景
const luckyBlocks = ref([
  {
    padding: '0px', // 外圈宽度，使用图片时设为0
    background: 'transparent', // 透明背景，让图片显示
    imgs: [
      {
        src: wheelBgImg,
        top: '0px',
        width: '100%',
        height: '100%',
        rotate: true // 一体图跟随转盘旋转
      }
    ]
  }
])

const luckyPrizes = ref<any[]>([])

const luckyButtons = ref([{
  radius: '80px', // 进一步增大按钮尺寸
  background: 'transparent', // 透明背景，让图片显示
  pointer: true,
  fonts: [], // 移除文字，使用图片
  imgs: [
    {
      src: startBtnImg,
      top: '-60px', // 调整位置使图片居中
      width: '120px', // 进一步增大图片尺寸
      height: '120px'
    }
  ]
}])

// 转盘默认配置 - 适配图片背景
const luckyDefaultConfig = ref({
  gutter: '0px', // 扇形间隙，使用图片时设为0
  stopRange: 0.8,
  speed: 15, // 转动速度
  accelerationTime: 3000, // 加速时间
  decelerationTime: 3000, // 减速时间
  offsetDegree: 0
})

// 转盘默认样式 - 优化文字显示
const luckyDefaultStyle = ref({
  fontColor: '#ffffff',
  fontSize: '16px',
  fontStyle: 'Arial',
  fontWeight: 'bold',
  lineHeight: '18px',
  wordWrap: false,
  lengthLimit: '90%'
})

// Lucky Canvas 转盘引用
const myLucky = ref<any>(null)

// API数据状态
const isLoading = ref(false)
const currentPeriod = ref<any>(null)
const userStatus = ref<any>(null)
const hasActiveActivity = ref(true) // 是否有活动期次

// 统计数据
const totalPrizePool = ref(0)

// 随机中奖公告数据
const winningAnnouncements = ref<any[]>([])

// 生成随机用户ID
const generateRandomUserId = () => {
  return String(Math.floor(Math.random() * 9000) + 1000)
}

// 获取当前期次信息
const fetchCurrentPeriod = async () => {
  try {
    const response = await userAPI.getCurrentPeriod()

    if (response.data.success) {
      hasActiveActivity.value = true
      currentPeriod.value = response.data.data.period

      // 设置奖品数据
      prizes.value = currentPeriod.value.prizes || []

      // 设置奖池金额
      totalPrizePool.value = parseFloat(currentPeriod.value.totalPrizeAmount) || 0

      // 启动倒计时
      startCountdown()

      return true
    } else {
      // 检查是否为"当前没有活动期次"的情况
      if (response.data.error === "当前没有活动期次") {
        hasActiveActivity.value = false
        currentPeriod.value = null
        prizes.value = []
        totalPrizePool.value = 0
        console.log('当前没有活动期次')
        return true // 返回true表示请求成功，只是没有活动
      }
      return false
    }
  } catch (error) {
    console.error('获取当前期次失败:', error)
    ElMessage.error('获取期次信息失败')
    hasActiveActivity.value = false
    return false
  }
}

// 获取用户状态
const fetchUserStatus = async () => {
  if (!authStore.isAuthenticated) {
    console.log('👤 用户未登录，设置默认状态')
    // 如果未登录，设置默认状态
    userStatus.value = {
      user: null,
      currentPeriod: null,
      remainingShares: 0,
      remainingDailyAttempts: 0,
      usedShares: 0,
      usedDailyAttempts: 0,
      totalRecords: 0,
      winRecords: 0
    }
    return true
  }

  try {
    const response = await userAPI.getUserStatus()

    if (response.data.success) {
      userStatus.value = response.data.data
      return true
    }
  } catch (error) {
    console.error('获取用户状态失败:', error)

    // 检查是否为 AxiosError
    if (axios.isAxiosError(error)) {
      console.error('错误详情:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        }
      })

      // 如果是401错误（token过期或无效），设置为未登录状态
      if (error.response?.status === 401) {
        console.log('🔐 认证失败，设置为未登录状态')
        // 设置默认状态（相当于未登录）
        userStatus.value = {
          user: null,
          currentPeriod: null,
          remainingShares: 0,
          remainingDailyAttempts: 0,
          usedShares: 0,
          usedDailyAttempts: 0,
          totalRecords: 0,
          winRecords: 0
        }
        return true // 返回true，因为这是正常的未登录状态
      }
    } else {
      // 非 Axios 错误
      console.error('非网络错误:', error)
    }

    // 其他错误，设置默认状态
    userStatus.value = {
      user: null,
      currentPeriod: null,
      remainingAttempts: 0,
      totalRecords: 0,
      winRecords: 0
    }
    return false
  }
}

// 生成随机中奖公告（使用真实奖品数据）
const generateWinningAnnouncement = () => {
  if (prizes.value.length === 0) {
    return null
  }

  // 使用真实的奖品数据，但随机选择
  const availablePrizes = prizes.value.filter(prize => prize.prizeAmount > 0)
  if (availablePrizes.length === 0) {
    return null
  }

  const selectedPrize = availablePrizes[Math.floor(Math.random() * availablePrizes.length)]

  return {
    id: Date.now() + Math.random(),
    userId: generateRandomUserId(),
    prizeName: selectedPrize.prizeName,
    prizeAmount: selectedPrize.prizeAmount,
    timeAgo: Math.floor(Math.random() * 30) + 1 // 1-30分钟前
  }
}

// 初始化中奖公告
const initWinningAnnouncements = () => {
  winningAnnouncements.value = []

  // 如果没有奖品数据，创建默认公告
  if (!prizes.value || prizes.value.length === 0) {
    const defaultAnnouncements = [
      { id: 1, userId: '1234', prizeName: '谢谢参与', prizeAmount: 0, timeAgo: 5 },
      { id: 2, userId: '5678', prizeName: '谢谢参与', prizeAmount: 0, timeAgo: 10 },
      { id: 3, userId: '9012', prizeName: '谢谢参与', prizeAmount: 0, timeAgo: 15 },
      { id: 4, userId: '3456', prizeName: '谢谢参与', prizeAmount: 0, timeAgo: 20 }
    ]
    winningAnnouncements.value = defaultAnnouncements
    return
  }

  // 使用真实奖品数据生成公告
  for (let i = 0; i < 8; i++) {
    const announcement = generateWinningAnnouncement()
    if (announcement) {
      winningAnnouncements.value.push(announcement)
    }
  }

  // 如果生成的公告不足，补充默认公告
  while (winningAnnouncements.value.length < 4) {
    winningAnnouncements.value.push({
      id: Date.now() + Math.random(),
      userId: generateRandomUserId(),
      prizeName: '谢谢参与',
      prizeAmount: 0,
      timeAgo: Math.floor(Math.random() * 30) + 1
    })
  }
}



// 定时添加新的中奖公告
const startAnnouncementRotation = () => {
  setInterval(() => {
    const newAnnouncement = generateWinningAnnouncement()
    if (newAnnouncement) {
      // 移除最后一个，在开头添加新的
      winningAnnouncements.value.pop()
      winningAnnouncements.value.unshift(newAnnouncement)
    }
  }, 3000) // 每3秒更新一次
}

// 启动倒计时定时器
const startCountdown = () => {
  updateTimeRemaining()
  setInterval(updateTimeRemaining, 1000)
}



// 初始化Lucky Canvas奖品数据 - 使用图片背景
const initLuckyPrizes = () => {
  // 如果没有奖品数据，使用默认奖品
  const prizesToUse = prizes.value && prizes.value.length > 0 ? prizes.value : [
    { prizeName: '谢谢参与', prizeAmount: 0, probability: 1 }
  ]

  luckyPrizes.value = prizesToUse.map((prize) => {
    return {
      range: Math.round((prize.probability || 1) * 100),
      background: 'transparent', // 透明背景，让转盘背景图显示
      fonts: [], // 移除所有文字，图片上已经配置好了
      imgs: [], // 移除奖品图片，让转盘背景图完全显示
      // 保存奖品信息用于查找索引
      prizeName: prize.prizeName,
      prizeAmount: prize.prizeAmount
    }
  })
}

// 根据奖品名称查找转盘索引
function findPrizeIndexByName(prizeName: string): number {
  // 直接使用luckyPrizes中的奖品信息来查找索引
  const index = luckyPrizes.value.findIndex(prize => prize.prizeName === prizeName)
  return index >= 0 ? index : 0 // 如果找不到，默认返回第一个
}

// Lucky Canvas 回调方法
const startCallback = async () => {
  // 前置验证
  if (!authStore.isAuthenticated) {
    ElMessage.warning('请先登录后参与抽奖')
    return
  }

  if (isSpinning.value) {
    ElMessage.warning('抽奖进行中，请稍候')
    return
  }

  if (!userStatus.value || userStatus.value.remainingShares <= 0) {
    ElMessage.warning('本期抽奖份数已用完')
    return
  }

  if (!userStatus.value || userStatus.value.remainingDailyAttempts <= 0) {
    ElMessage.warning('今日抽奖次数已用完')
    return
  }

  if (!currentPeriod.value || currentPeriod.value.status !== '进行中') {
    ElMessage.warning('当前期次未开始或已结束')
    return
  }

  // 清空之前的抽奖结果
  lotteryResult.value = null
  isSpinning.value = true

  // 调用抽奖组件的 play 方法开始游戏
  myLucky.value?.play()

  // 模拟调用接口异步抽奖
  userAPI.drawLottery(currentPeriod.value.id)
    .then(response => {
      if (response.data.success) {
        const result = response.data.data

        // 根据奖品名称找到转盘索引
        const prizeIndex = findPrizeIndexByName(result.record.prizeName)

        // 记录抽奖结果用于 endCallback
        lotteryResult.value = {
          prizeName: result.record.prizeName,
          prizeAmount: result.record.prizeAmount,
          isWinner: result.record.isWinner,
          configSource: result.configSource,
          remainingShares: result.remainingShares,
          remainingDailyAttempts: result.remainingDailyAttempts
        }

        // 模拟接口请求时间，3秒后停止转盘
        setTimeout(() => {
          // 调用 stop 停止旋转并传递中奖索引
          myLucky.value?.stop(prizeIndex)
        }, 3000)

      } else {
        // 处理抽奖失败
        console.error('❌ 抽奖失败:', response.data.error)
        lotteryResult.value = {
          isError: true,
          errorMessage: response.data.error
        }

        setTimeout(() => {
          myLucky.value?.stop(0)
        }, 3000)
      }
    })
    .catch(error => {
      console.error('💥 抽奖请求失败:', error)
      lotteryResult.value = {
        isError: true,
        errorMessage: '抽奖失败，请稍后重试'
      }

      setTimeout(() => {
        myLucky.value?.stop(0)
      }, 3000)
    })
}

const endCallback = () => {
  isSpinning.value = false

  // 显示抽奖结果（基于真实API返回的数据）
  if (lotteryResult.value) {
    // 检查是否有错误
    if (lotteryResult.value.isError) {
      ElMessage.error(lotteryResult.value.errorMessage)
      return
    }

    // 更新用户状态数据
    if (userStatus.value && ('remainingShares' in lotteryResult.value || 'remainingDailyAttempts' in lotteryResult.value)) {
      if ('remainingShares' in lotteryResult.value) {
        userStatus.value.remainingShares = lotteryResult.value.remainingShares
      }
      if ('remainingDailyAttempts' in lotteryResult.value) {
        userStatus.value.remainingDailyAttempts = lotteryResult.value.remainingDailyAttempts
      }

      // 如果中奖，更新累计中奖次数和总奖金
      if (lotteryResult.value.isWinner) {
        userStatus.value.winRecords = (userStatus.value.winRecords || 0) + 1

        // 更新用户总奖金
        if (userStatus.value.user && lotteryResult.value.prizeAmount) {
          userStatus.value.user.totalWinnings = (userStatus.value.user.totalWinnings || 0) + lotteryResult.value.prizeAmount
        }
      }

      // 更新总抽奖次数
      userStatus.value.totalRecords = (userStatus.value.totalRecords || 0) + 1
    }

    // 显示抽奖结果弹窗
    showLotteryResult.value = true

    if (lotteryResult.value.isWinner) {
      // 添加到中奖公告（使用真实数据）
      const newAnnouncement = {
        id: Date.now(),
        userId: generateRandomUserId(),
        prizeName: lotteryResult.value.prizeName,
        prizeAmount: lotteryResult.value.prizeAmount,
        timeAgo: 0
      }
      winningAnnouncements.value.unshift(newAnnouncement)
      if (winningAnnouncements.value.length > 10) {
        winningAnnouncements.value.pop()
      }
    }

    // 显示配置来源信息（调试用）
    if (lotteryResult.value.configSource) {
      // 配置来源信息已移除调试日志
    }
  } else {
    console.warn('⚠️ 没有抽奖结果数据')
    ElMessage.info('抽奖完成')
  }
}

// 初始化页面数据
const initPageData = async () => {
  isLoading.value = true

  try {
    // 获取当前期次信息
    const periodSuccess = await fetchCurrentPeriod()
    if (!periodSuccess) {
      ElMessage.error('无法获取期次信息')
      return
    }

    // 如果没有活动期次，只显示无活动状态，不继续初始化其他内容
    if (!hasActiveActivity.value) {
      console.log('当前没有活动期次，显示无活动状态')
      return
    }

    // 如果用户已登录，获取用户状态
    if (authStore.isAuthenticated) {
      const userStatusSuccess = await fetchUserStatus()
      if (!userStatusSuccess) {
        console.warn('⚠️ 获取用户状态失败，但继续初始化页面')
      }
    } else {
      console.log('👤 用户未登录，跳过用户状态获取')
    }

    // 初始化转盘奖品
    initLuckyPrizes()

    // 初始化中奖公告
    initWinningAnnouncements()
    startAnnouncementRotation()

    // 启动倒计时
    startCountdown()

  } catch (error) {
    console.error('初始化页面数据失败:', error)
    ElMessage.error('页面初始化失败')
  } finally {
    isLoading.value = false
  }
}

// 清理完成，转盘配置已优化



// 清理完成，旧的SVG转盘代码已删除



onMounted(() => {
  initPageData()
  // 添加窗口大小变化监听
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', handleResize)
  }
})

onUnmounted(() => {
  // 清理事件监听器
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', handleResize)
  }
})
</script>

<style scoped>
/* 转盘旋转动画优化 */
.transition-transform {
  transition: transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 卡片悬停效果 */
.bg-gray-700\/50 {
  transition: all 0.3s ease-in-out;
}

/* 背景渐变动画 */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 光效动画 */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 215, 0, 0.6);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.7);
}

/* 金色主题色彩 */
.text-gold-400 {
  color: #fbbf24;
}

.border-gold-500 {
  border-color: #f59e0b;
}

.bg-gold-400 {
  background-color: #fbbf24;
}

.bg-gold-500 {
  background-color: #f59e0b;
}

.bg-gold-600 {
  background-color: #d97706;
}

.border-gold-500\/30 {
  border-color: rgba(245, 158, 11, 0.3);
}

.border-gold-500\/50 {
  border-color: rgba(245, 158, 11, 0.5);
}

.from-gold-400 {
  --tw-gradient-from: #fbbf24;
}

.to-gold-500 {
  --tw-gradient-to: #f59e0b;
}

.from-gold-500 {
  --tw-gradient-from: #f59e0b;
}

.to-gold-600 {
  --tw-gradient-to: #d97706;
}

.hover\:from-gold-400:hover {
  --tw-gradient-from: #fbbf24;
}

.hover\:to-gold-500:hover {
  --tw-gradient-to: #f59e0b;
}

.hover\:from-gold-500:hover {
  --tw-gradient-from: #f59e0b;
}

.hover\:to-gold-400:hover {
  --tw-gradient-to: #fbbf24;
}

/* 3D转盘动画效果 */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

/* 光环效果 */
@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

.animate-glow-pulse {
  animation: glow-pulse 3s ease-in-out infinite;
}

/* 悬浮效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 垂直跑马灯容器 */
.marquee-container {
  height: 288px;
  overflow: hidden;
  position: relative;
  mask: linear-gradient(to bottom,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%);
  -webkit-mask: linear-gradient(to bottom,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%);
}

/* 跑马灯内容 */
.marquee-content {
  display: flex;
  flex-direction: column;
  animation: marquee-vertical 20s linear infinite;
}

/* 慢速滚动 */
.marquee-slow {
  animation: marquee-vertical 30s linear infinite;
}

/* 跑马灯组 */
.marquee-group {
  flex-shrink: 0;
}

/* 垂直滚动动画 */
@keyframes marquee-vertical {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

/* 悬停暂停动画 */
.marquee-container:hover .marquee-content {
  animation-play-state: paused;
}

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 流畅滚动 */
.smooth-scroll {
  scroll-behavior: smooth;
  scroll-snap-type: y proximity;
}

/* 滚动项目对齐 */
.scroll-item {
  scroll-snap-align: start;
  scroll-margin-top: 8px;
}

/* 自定义滚动条样式（备用） */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.6);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.8);
}

/* 平滑滚动 */
.custom-scrollbar {
  scroll-behavior: smooth;
}

/* 毛玻璃效果增强 */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* 阴影效果 */
.shadow-glow {
  box-shadow: 0 0 30px rgba(212, 175, 55, 0.3);
}

.shadow-glow:hover {
  box-shadow: 0 0 50px rgba(212, 175, 55, 0.5);
}

/* 按钮发光效果 */
.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-glow:hover::before {
  left: 100%;
}

/* 星光闪烁效果 */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

/* 滚动淡入效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

/* 滚动容器增强 */
.enhanced-scroll {
  position: relative;
}

.enhanced-scroll::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, rgba(51, 65, 85, 0.8), transparent);
  pointer-events: none;
  z-index: 1;
}

.enhanced-scroll::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(51, 65, 85, 0.8), transparent);
  pointer-events: none;
  z-index: 1;
}
</style>


