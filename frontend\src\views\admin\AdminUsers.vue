<template>
  <div class="admin-users min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-6">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-3xl font-bold text-white mb-2 flex items-center">
            <el-icon class="mr-3 text-gold-400" size="32"><User /></el-icon>
            用户管理
          </h2>
          <p class="text-gray-400">管理平台用户信息和状态</p>
        </div>
        <div class="flex items-center space-x-4">
          <div class="px-4 py-2 bg-gradient-to-r from-gold-500/20 to-gold-600/20 border border-gold-500/30 rounded-lg">
            <span class="text-gold-400 text-sm font-medium">总用户数: {{ totalUsers }}</span>
          </div>
          <button
            @click="handleRefresh"
            class="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400 hover:text-blue-300 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <el-icon><Refresh /></el-icon>
            <span>刷新数据</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl p-6 mb-6">
      <div class="flex flex-wrap gap-4 items-center">
        <div class="flex-1 min-w-[300px]">
          <input
            v-model="searchKeyword"
            placeholder="搜索用户ID或用户名..."
            class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
          />
        </div>

        <select
          v-model="statusFilter"
          class="px-4 py-3 bg-gray-700 border border-gray-600 text-white rounded-lg focus:outline-none focus:border-gold-500 min-w-[150px]"
        >
          <option value="">全部状态</option>
          <option value="正常">正常</option>
          <option value="禁用">禁用</option>
        </select>

        <button
          @click="handleSearch"
          class="px-6 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black font-medium rounded-lg transition-all duration-200 flex items-center space-x-2"
        >
          <el-icon><Search /></el-icon>
          <span>搜索</span>
        </button>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-xl overflow-hidden">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-700">
        <h3 class="text-lg font-semibold text-white flex items-center">
          <el-icon class="mr-2 text-gold-400"><List /></el-icon>
          用户列表
        </h3>
      </div>

      <!-- 加载状态 -->
      <div v-if="adminStore.isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="w-8 h-8 border-2 border-gold-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p class="text-gray-400">加载中...</p>
        </div>
      </div>

      <!-- 用户数据表 -->
      <div v-else-if="paginatedUsers.length > 0" class="p-6 overflow-x-auto">
        <el-table
          :data="paginatedUsers"
          style="width: 100%; --el-table-bg-color: transparent; --el-table-tr-bg-color: transparent;"
          class="admin-table"
          :header-cell-style="{
            backgroundColor: '#374151',
            color: '#f3f4f6',
            borderColor: '#4b5563',
            fontSize: '14px',
            fontWeight: '600'
          }"
          :cell-style="{
            backgroundColor: 'transparent',
            color: '#f3f4f6',
            borderColor: '#4b5563',
            padding: '12px 8px'
          }"
          :row-style="{ backgroundColor: 'transparent' }"
          stripe
          table-layout="fixed"
        >
          <el-table-column prop="id" label="用户ID" width="80" align="center" />

          <el-table-column label="用户信息" min-width="180">
            <template #default="scope">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <span class="text-sm font-bold text-black">{{ scope.row.username.charAt(0).toUpperCase() }}</span>
                </div>
                <div class="min-w-0 flex-1">
                  <div class="text-white font-medium truncate">{{ scope.row.username }}</div>
                  <div class="text-gray-400 text-xs truncate">{{ scope.row.platformId }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态" width="90" align="center">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === '正常' ? 'success' : 'danger'"
                size="small"
                effect="dark"
              >
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="抽奖统计" width="120" align="center">
            <template #default="scope">
              <div class="text-center">
                <div class="text-blue-400 text-sm">{{ scope.row.lotteryCount || 0 }}次</div>
                <div class="text-green-400 text-xs">中奖{{ scope.row.winCount || 0 }}次</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="总中奖金额" width="130" align="center">
            <template #default="scope">
              <span class="text-gold-400 font-medium">¥{{ formatAmount(scope.row.totalWinnings || 0) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="注册时间" width="120" align="center">
            <template #default="scope">
              <span class="text-gray-300 text-sm">{{ formatDate(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="240" align="center" fixed="right">
            <template #default="scope">
              <div class="flex justify-center space-x-1 flex-wrap gap-1">
                <el-button
                  v-if="scope.row.status === '正常'"
                  @click="handleUpdateStatus(scope.row.id, '禁用')"
                  type="danger"
                  size="small"
                  plain
                >
                  禁用
                </el-button>
                <el-button
                  v-else
                  @click="handleUpdateStatus(scope.row.id, '正常')"
                  type="success"
                  size="small"
                  plain
                >
                  启用
                </el-button>

                <el-button
                  @click="handleEditUser(scope.row)"
                  type="info"
                  size="small"
                  plain
                >
                  编辑
                </el-button>

                <el-button
                  @click="handleViewDetails(scope.row)"
                  type="primary"
                  size="small"
                  plain
                >
                  详情
                </el-button>

                <el-button
                  @click="handleLotteryConfig(scope.row)"
                  type="warning"
                  size="small"
                  plain
                >
                  抽奖率
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <el-icon size="64" class="text-gray-600 mb-4"><User /></el-icon>
          <p class="text-gray-400 text-lg mb-2">暂无用户数据</p>
          <p class="text-gray-500 text-sm">请检查搜索条件或稍后重试</p>
        </div>
      </div>

      <!-- 分页 -->
      <AdminPagination
        v-model:current-page="currentPage"
        :total-pages="totalPages"
        :total="totalUsers"
        :page-size="pageSize"
        @page-change="handlePageChange"
      />
    </div>

    <!-- 编辑用户对话框 -->
    <div v-if="editDialogVisible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl w-full max-w-md">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-3 text-gold-400" size="24"><Edit /></el-icon>
            编辑用户
          </h3>
          <button
            @click="editDialogVisible = false"
            class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>

        <!-- 对话框内容 -->
        <div v-if="editingUser" class="p-6">
          <!-- 用户基本信息显示 -->
          <div class="mb-6 p-4 bg-gray-700/30 rounded-lg border border-gray-600/50">
            <div class="flex items-center space-x-3 mb-2">
              <div class="w-10 h-10 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center">
                <span class="text-sm font-bold text-black">{{ editingUser.username.charAt(0).toUpperCase() }}</span>
              </div>
              <div>
                <p class="text-white font-medium">{{ editingUser.username }}</p>
                <p class="text-gray-400 text-sm">{{ editingUser.platformId }}</p>
              </div>
            </div>
          </div>

          <!-- 编辑表单 -->
          <div class="space-y-4">
            <!-- 新密码 -->
            <div>
              <label class="block text-sm font-medium text-gray-400 mb-2">
                新密码
                <span class="text-gray-500 text-xs ml-1">(留空则不修改)</span>
              </label>
              <input
                v-model="editForm.password"
                type="password"
                placeholder="请输入新密码（至少6位）"
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
              />
            </div>

            <!-- 确认密码 -->
            <div v-if="editForm.password">
              <label class="block text-sm font-medium text-gray-400 mb-2">确认新密码</label>
              <input
                v-model="editForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                class="w-full px-4 py-3 bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg focus:outline-none focus:border-gold-500 focus:ring-1 focus:ring-gold-500 transition-all"
              />
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-700">
            <button
              @click="editDialogVisible = false"
              class="px-4 py-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
            >
              取消
            </button>
            <button
              @click="handleSaveEdit"
              :disabled="editForm.password && editForm.password !== editForm.confirmPassword"
              class="px-6 py-2 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed text-black font-medium rounded-lg transition-all"
            >
              保存
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <div v-if="detailDialogVisible" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-auto">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <el-icon class="mr-3 text-gold-400" size="24"><User /></el-icon>
            用户详情
          </h3>
          <button
            @click="detailDialogVisible = false"
            class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-all"
          >
            <el-icon size="20"><Close /></el-icon>
          </button>
        </div>

        <!-- 对话框内容 -->
        <div v-if="selectedUser" class="p-6">
          <!-- 用户头像和基本信息 -->
          <div class="flex items-center space-x-4 mb-6 p-4 bg-gray-700/30 rounded-lg border border-gray-600/50">
            <div class="w-16 h-16 bg-gradient-to-br from-gold-400 to-gold-600 rounded-full flex items-center justify-center">
              <span class="text-2xl font-bold text-black">{{ selectedUser.username.charAt(0).toUpperCase() }}</span>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold text-white mb-1">{{ selectedUser.username }}</h4>
              <p class="text-gray-400">平台ID: {{ selectedUser.platformId }}</p>
              <div :class="selectedUser.status === '正常' ? 'bg-green-500/20 border-green-500/30 text-green-400' : 'bg-red-500/20 border-red-500/30 text-red-400'" class="inline-flex px-3 py-1 border rounded-full mt-2">
                <span class="text-sm font-medium">{{ selectedUser.status }}</span>
              </div>
            </div>
          </div>

          <!-- 详细信息网格 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                <label class="block text-sm font-medium text-gray-400 mb-2">用户ID</label>
                <p class="text-white text-lg font-medium">{{ selectedUser.id }}</p>
              </div>

              <div class="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                <label class="block text-sm font-medium text-gray-400 mb-2">用户名</label>
                <p class="text-white text-lg font-medium">{{ selectedUser.username }}</p>
              </div>

              <div class="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                <label class="block text-sm font-medium text-gray-400 mb-2">账户状态</label>
                <div :class="selectedUser.status === '正常' ? 'bg-green-500/20 border-green-500/30 text-green-400' : 'bg-red-500/20 border-red-500/30 text-red-400'" class="inline-flex px-3 py-1 border rounded-full">
                  <span class="text-sm font-medium">{{ selectedUser.status }}</span>
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <div class="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                <label class="block text-sm font-medium text-gray-400 mb-2">平台ID</label>
                <p class="text-blue-400 text-lg font-medium">{{ selectedUser.platformId }}</p>
              </div>

              <div class="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                <label class="block text-sm font-medium text-gray-400 mb-2">总中奖金额</label>
                <p class="text-gold-400 text-xl font-bold">¥{{ formatAmount(selectedUser.totalWinnings) }}</p>
              </div>

              <div class="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                <label class="block text-sm font-medium text-gray-400 mb-2">注册时间</label>
                <p class="text-white text-lg font-medium">{{ formatDate(selectedUser.createdAt) }}</p>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-700">
            <button
              @click="detailDialogVisible = false"
              class="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg transition-all"
            >
              关闭
            </button>
            <button
              v-if="selectedUser.status === '正常'"
              @click="handleUpdateStatus(selectedUser.id, '禁用'); detailDialogVisible = false"
              class="px-6 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all"
            >
              禁用用户
            </button>
            <button
              v-else
              @click="handleUpdateStatus(selectedUser.id, '正常'); detailDialogVisible = false"
              class="px-6 py-2 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400 hover:text-green-300 rounded-lg transition-all"
            >
              启用用户
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 抽奖率配置对话框 -->
    <div
      v-if="lotteryConfigDialogVisible"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      @click.self="lotteryConfigDialogVisible = false"
    >
      <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gold-500/30 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <!-- 对话框头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h3 class="text-xl font-bold text-white flex items-center">
              <el-icon class="mr-2 text-gold-400" size="24"><Setting /></el-icon>
              抽奖率配置
            </h3>
            <p class="text-gray-400 text-sm mt-1">
              用户：{{ currentConfigUser?.username }} (ID: {{ currentConfigUser?.id }})
            </p>
          </div>
          <button
            @click="lotteryConfigDialogVisible = false"
            class="text-gray-400 hover:text-white transition-colors"
          >
            <el-icon size="24"><Close /></el-icon>
          </button>
        </div>

        <!-- 对话框内容 -->
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <!-- 总开关 -->
          <div class="mb-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-white font-medium mb-1">个人抽奖率设置</h4>
                <p class="text-gray-400 text-sm">启用后将使用个人配置的中奖率，否则使用全局设置</p>
              </div>
              <el-switch
                v-model="currentConfigUser!.customLotteryEnabled"
                size="large"
                active-color="#d4af37"
                inactive-color="#374151"
              />
            </div>
          </div>

          <!-- 快捷操作和概率显示 -->
          <div class="mb-6 flex justify-between items-center">
            <div class="flex space-x-3">
              <button
                @click="setUserNoWin"
                class="px-4 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 text-red-400 hover:text-red-300 rounded-lg transition-all flex items-center space-x-2"
              >
                <el-icon><Close /></el-icon>
                <span>一键设置不中奖</span>
              </button>
            </div>

            <!-- 总概率显示 -->
            <div class="flex items-center space-x-3">
              <span class="text-gray-300">总概率：</span>
              <span :class="['font-bold text-lg', probabilityStatus.color]">
                {{ probabilityStatus.text }}
              </span>
              <div v-if="!isProbabilityValid" class="flex items-center space-x-1 text-red-400">
                <el-icon><Warning /></el-icon>
                <span class="text-sm">必须为100%</span>
              </div>
              <div v-else class="flex items-center space-x-1 text-green-400">
                <el-icon><Check /></el-icon>
                <span class="text-sm">概率正确</span>
              </div>
            </div>
          </div>

          <!-- 奖品配置列表 -->
          <div v-if="isLoadingConfigs" class="text-center py-8">
            <el-icon class="animate-spin text-gold-400 text-2xl"><Loading /></el-icon>
            <p class="text-gray-400 mt-2">加载配置中...</p>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="config in userPrizeConfigs"
              :key="config.prizeConfigId"
              class="bg-gradient-to-r from-gray-700/50 to-gray-800/50 border border-gray-600 rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-3">
                <div>
                  <h5 class="text-white font-medium">{{ config.prizeName }}</h5>
                  <p class="text-gray-400 text-sm">
                    奖品金额：{{ config.prizeAmount }}元 | 全局概率：{{ (config.globalProbability * 100).toFixed(2) }}%
                  </p>
                </div>
                <el-switch
                  v-model="config.isEnabled"
                  size="default"
                  active-color="#d4af37"
                  inactive-color="#374151"
                />
              </div>

              <div class="flex items-center space-x-4">
                <span class="text-gray-300 text-sm min-w-[80px]">个人概率：</span>
                <div class="flex-1">
                  <el-slider
                    v-model="config.customProbability"
                    :min="0"
                    :max="1"
                    :step="0.0001"
                    :format-tooltip="(val: number) => `${(val * 100).toFixed(2)}%`"
                    class="custom-slider"
                  />
                </div>
                <div class="min-w-[80px] text-right">
                  <span class="text-gold-400 font-medium">
                    {{ (config.customProbability * 100).toFixed(2) }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 对话框底部 -->
        <div class="flex justify-end space-x-3 p-6 border-t border-gray-700">
          <button
            @click="lotteryConfigDialogVisible = false"
            class="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white rounded-lg transition-all"
          >
            取消
          </button>
          <button
            @click="saveUserPrizeConfigs"
            :disabled="isSavingConfigs || !isProbabilityValid"
            :class="[
              'px-6 py-2 font-medium rounded-lg transition-all flex items-center space-x-2',
              isProbabilityValid && !isSavingConfigs
                ? 'bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-black'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            ]"
          >
            <el-icon v-if="isSavingConfigs" class="animate-spin"><Loading /></el-icon>
            <span>{{ isSavingConfigs ? '保存中...' : '保存配置' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAdminStore } from '../../stores/admin'
import { adminAPI } from '../../api/config'
import AdminPagination from '../../components/AdminPagination.vue'

const adminStore = useAdminStore()

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 分页信息
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
})

// 用户类型定义
interface User {
  id: number
  username: string
  platformId: string
  status: string
  totalWinnings: number
  createdAt: string
  updatedAt: string
  lotteryCount?: number
  winCount?: number
  totalWinAmount?: number
  customLotteryEnabled?: boolean
}

// 奖品配置类型定义
interface PrizeConfig {
  prizeConfigId: number
  prizeName: string
  prizeAmount: number
  globalProbability: number
  customProbability: number
  isEnabled: boolean
}

// 对话框
const detailDialogVisible = ref(false)
const selectedUser = ref<User | null>(null)

// 编辑用户对话框
const editDialogVisible = ref(false)
const editingUser = ref<User | null>(null)
const editForm = reactive({
  password: '',
  confirmPassword: ''
})

// 抽奖率配置对话框
const lotteryConfigDialogVisible = ref(false)
const currentConfigUser = ref<User | null>(null)
const userPrizeConfigs = ref<PrizeConfig[]>([])
const isLoadingConfigs = ref(false)
const isSavingConfigs = ref(false)

// 计算总概率
const totalProbability = computed(() => {
  return userPrizeConfigs.value.reduce((sum, config) => {
    return sum + (config.isEnabled ? config.customProbability : 0)
  }, 0)
})

// 检查概率是否为100%
const isProbabilityValid = computed(() => {
  return Math.abs(totalProbability.value - 1.0) < 0.0001
})

// 概率状态提示
const probabilityStatus = computed(() => {
  const percentage = (totalProbability.value * 100).toFixed(2)
  if (isProbabilityValid.value) {
    return { text: `${percentage}%`, color: 'text-green-400', valid: true }
  } else {
    return { text: `${percentage}%`, color: 'text-red-400', valid: false }
  }
})

// 当前显示的用户（直接使用store中的数据，因为后端已经处理了分页和筛选）
const paginatedUsers = computed(() => {
  return adminStore.users || []
})

// 总页数（使用后端返回的分页信息）
const totalPages = computed(() => {
  return pagination.value.totalPages
})

// 总用户数（使用后端返回的分页信息）
const totalUsers = computed(() => {
  return pagination.value.total
})

// 格式化金额
const formatAmount = (amount: number) => {
  return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadUsers()
}

// 刷新
const handleRefresh = async () => {
  await loadUsers()
  ElMessage.success('用户列表已刷新')
}

// 更新用户状态
const handleUpdateStatus = async (userId: number, status: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要${status === '禁用' ? '禁用' : '启用'}该用户吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const result = await adminStore.updateUserStatus(userId, status)
    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.error(result.message)
    }
  } catch {
    // 用户取消
  }
}

// 查看用户详情
const handleViewDetails = (user: any) => {
  selectedUser.value = user
  detailDialogVisible.value = true
}

// 编辑用户
const handleEditUser = (user: User) => {
  editingUser.value = user
  editForm.password = ''
  editForm.confirmPassword = ''
  editDialogVisible.value = true
}

// 保存编辑
const handleSaveEdit = async () => {
  if (!editingUser.value) return

  // 验证密码
  if (editForm.password) {
    if (editForm.password.length < 6) {
      ElMessage.error('密码长度至少6位')
      return
    }
    if (editForm.password !== editForm.confirmPassword) {
      ElMessage.error('两次输入的密码不一致')
      return
    }
  }

  try {
    const updateData: any = {}

    // 只有输入了密码才更新
    if (editForm.password && editForm.password.trim() !== '') {
      updateData.password = editForm.password
    }

    // 如果没有任何更新内容
    if (Object.keys(updateData).length === 0) {
      ElMessage.warning('没有需要更新的内容')
      return
    }

    const result = await adminAPI.updateUser(editingUser.value.id, updateData)

    if (result.data.success) {
      ElMessage.success('用户信息更新成功')
      editDialogVisible.value = false
      // 重新加载用户列表
      await loadUsers()
    } else {
      ElMessage.error(result.data.message || '更新失败')
    }
  } catch (error: any) {
    console.error('更新用户信息失败:', error)
    ElMessage.error(error.response?.data?.message || '更新用户信息失败')
  }
}

// 页面变化处理
const handlePageChange = (page: number) => {
  loadUsers()
}

// 处理抽奖率配置
const handleLotteryConfig = async (user: User) => {
  // 确保 customLotteryEnabled 字段有正确的值
  currentConfigUser.value = {
    ...user,
    customLotteryEnabled: user.customLotteryEnabled || false
  }
  lotteryConfigDialogVisible.value = true
  await loadUserPrizeConfigs(user.id)
}

// 加载用户抽奖率配置
const loadUserPrizeConfigs = async (userId: number) => {
  isLoadingConfigs.value = true
  try {
    const response = await adminAPI.getUserPrizeConfigs(userId)
    if (response.data.success) {
      const data = response.data.data
      userPrizeConfigs.value = data.configs || []
      // 更新用户的个人抽奖率开关状态
      if (currentConfigUser.value) {
        currentConfigUser.value.customLotteryEnabled = data.customLotteryEnabled
      }
    } else {
      throw new Error(response.data.message || '获取配置失败')
    }
  } catch (error: any) {
    console.error('加载用户抽奖率配置失败:', error)
    ElMessage.error(error.response?.data?.message || '加载抽奖率配置失败')
  } finally {
    isLoadingConfigs.value = false
  }
}

// 保存用户抽奖率配置
const saveUserPrizeConfigs = async () => {
  if (!currentConfigUser.value) return

  // 验证总概率是否为100%
  if (!isProbabilityValid.value) {
    ElMessage.error(`总概率必须为100%，当前为${(totalProbability.value * 100).toFixed(2)}%，请调整后再保存`)
    return
  }

  isSavingConfigs.value = true
  try {
    const response = await adminAPI.updateUserPrizeConfigs(currentConfigUser.value.id, {
      configs: userPrizeConfigs.value,
      customLotteryEnabled: currentConfigUser.value.customLotteryEnabled || false,
      totalProbability: totalProbability.value
    })

    if (response.data.success) {
      ElMessage.success('抽奖率配置保存成功')
      lotteryConfigDialogVisible.value = false
      await loadUsers() // 刷新用户列表
    } else {
      throw new Error(response.data.message || '保存配置失败')
    }
  } catch (error: any) {
    console.error('保存用户抽奖率配置失败:', error)
    ElMessage.error(error.response?.data?.message || '保存抽奖率配置失败')
  } finally {
    isSavingConfigs.value = false
  }
}

// 一键设置不中奖
const setUserNoWin = async () => {
  if (!currentConfigUser.value) return

  try {
    await ElMessageBox.confirm(
      '确定要设置该用户为不中奖状态吗？除了"谢谢参与"外，其他奖品中奖率都将设为0%。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const response = await adminAPI.setUserNoWin(currentConfigUser.value.id)

    if (response.data.success) {
      ElMessage.success('已设置为不中奖状态')
      await loadUserPrizeConfigs(currentConfigUser.value.id) // 刷新配置
    } else {
      throw new Error(response.data.message || '设置失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('设置不中奖失败:', error)
      ElMessage.error(error.response?.data?.message || '设置不中奖失败')
    }
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    // 构建查询参数
    const params: any = {
      page: currentPage.value,
      limit: pageSize.value
    }

    // 添加搜索条件
    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }

    // 添加状态筛选
    if (statusFilter.value) {
      params.status = statusFilter.value
    }

    const result = await adminStore.fetchUsers(params)
    if (result.success && result.data) {
      // 更新分页信息
      pagination.value = result.data.pagination || {
        page: currentPage.value,
        limit: pageSize.value,
        total: 0,
        totalPages: 0
      }
    } else {
      ElMessage.error(result.message || '加载用户列表失败')
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  }
}

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.admin-users {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

/* 深色主题金色装饰 */
.text-gold-400 {
  color: #d4af37;
}

.bg-gold-500 {
  background-color: #d4af37;
}

.border-gold-500 {
  border-color: #d4af37;
}

/* 卡片悬停效果 */
.group:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 渐变背景动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient 8s ease infinite;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.5);
}

/* Element Plus 表格样式 */
.admin-table {
  --el-table-bg-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-header-bg-color: #374151;
  --el-table-header-text-color: #f3f4f6;
  --el-table-text-color: #f3f4f6;
  --el-table-border-color: #4b5563;
  --el-table-row-hover-bg-color: rgba(55, 65, 81, 0.5);
  background: transparent !important;
  width: 100% !important;
  min-width: 800px;
}

.admin-table .el-table__header-wrapper {
  background: transparent !important;
}

.admin-table .el-table__body-wrapper {
  background: transparent !important;
}

.admin-table .el-table__header {
  background-color: #374151 !important;
}

.admin-table .el-table__body {
  background: transparent !important;
}

.admin-table .el-table__row {
  background: transparent !important;
}

.admin-table .el-table__row:hover {
  background-color: rgba(55, 65, 81, 0.3) !important;
}

.admin-table .el-table__row--striped {
  background-color: rgba(55, 65, 81, 0.1) !important;
}

.admin-table .el-table__row--striped:hover {
  background-color: rgba(55, 65, 81, 0.3) !important;
}

.admin-table th.el-table__cell {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
  color: #f3f4f6 !important;
}

.admin-table td.el-table__cell {
  border-color: #4b5563 !important;
  color: #f3f4f6 !important;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .admin-users {
    padding: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .flex-wrap {
    flex-direction: column;
    align-items: stretch;
  }

  .min-w-300 {
    min-width: auto;
  }

  .min-w-150 {
    min-width: auto;
  }
}

@media (max-width: 640px) {
  .text-3xl {
    font-size: 1.5rem;
  }

  .space-x-4 > * + * {
    margin-left: 0.5rem;
  }

  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* 自定义滑块样式 */
.custom-slider :deep(.el-slider__runway) {
  background-color: #374151;
  border: 1px solid #4b5563;
}

.custom-slider :deep(.el-slider__bar) {
  background: linear-gradient(90deg, #d4af37, #f59e0b);
}

.custom-slider :deep(.el-slider__button) {
  border: 2px solid #d4af37;
  background-color: #1f2937;
}

.custom-slider :deep(.el-slider__button:hover) {
  border-color: #f59e0b;
  transform: scale(1.1);
}

.custom-slider :deep(.el-tooltip__trigger) {
  outline: none;
}
</style>
